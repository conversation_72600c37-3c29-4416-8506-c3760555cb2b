"""
LipNet Architecture for ICU Lipreading Project
Based on LipNet paper with BiGRU head for phrase classification
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional

class SpatioTemporalConv(nn.Module):
    """3D Convolutional block with batch normalization and activation"""
    
    def __init__(self, in_channels: int, out_channels: int, 
                 kernel_size: Tuple[int, int, int], 
                 stride: Tuple[int, int, int] = (1, 1, 1),
                 padding: Tuple[int, int, int] = (0, 0, 0),
                 dropout: float = 0.0):
        super().__init__()
        
        self.conv3d = nn.Conv3d(
            in_channels, out_channels, kernel_size, 
            stride=stride, padding=padding
        )
        self.bn = nn.BatchNorm3d(out_channels)
        self.activation = nn.ReLU(inplace=True)
        self.dropout = nn.Dropout3d(dropout) if dropout > 0 else None
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        x = self.conv3d(x)
        x = self.bn(x)
        x = self.activation(x)
        if self.dropout is not None:
            x = self.dropout(x)
        return x

class LipNetEncoder(nn.Module):
    """LipNet CNN encoder for spatiotemporal feature extraction"""
    
    def __init__(self, input_channels: int = 1, dropout: float = 0.3):
        super().__init__()
        
        # First conv block
        self.conv1 = SpatioTemporalConv(
            input_channels, 32, 
            kernel_size=(3, 5, 5), 
            stride=(1, 2, 2), 
            padding=(1, 2, 2),
            dropout=dropout
        )
        self.pool1 = nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2))
        
        # Second conv block
        self.conv2 = SpatioTemporalConv(
            32, 64, 
            kernel_size=(3, 3, 3), 
            stride=(1, 1, 1), 
            padding=(1, 1, 1),
            dropout=dropout
        )
        self.pool2 = nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2))
        
        # Third conv block
        self.conv3 = SpatioTemporalConv(
            64, 96, 
            kernel_size=(3, 3, 3), 
            stride=(1, 1, 1), 
            padding=(1, 1, 1),
            dropout=dropout
        )
        self.pool3 = nn.MaxPool3d(kernel_size=(1, 2, 2), stride=(1, 2, 2))
        
        # Calculate output dimensions
        # Input: (B, 1, T, 64, 96)
        # After conv1 + pool1: (B, 32, T, 16, 24)
        # After conv2 + pool2: (B, 64, T, 8, 12)  
        # After conv3 + pool3: (B, 96, T, 4, 6)
        self.feature_dim = 96 * 4 * 6  # 2304
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """
        Args:
            x: Input tensor (B, C, T, H, W)
        Returns:
            features: (B, T, feature_dim)
        """
        # Spatiotemporal convolutions
        x = self.conv1(x)
        x = self.pool1(x)
        
        x = self.conv2(x)
        x = self.pool2(x)
        
        x = self.conv3(x)
        x = self.pool3(x)
        
        # Reshape for RNN: (B, C, T, H, W) -> (B, T, C*H*W)
        B, C, T, H, W = x.size()
        x = x.permute(0, 2, 1, 3, 4).contiguous()  # (B, T, C, H, W)
        x = x.view(B, T, C * H * W)  # (B, T, feature_dim)
        
        return x

class BiGRUClassifier(nn.Module):
    """Bidirectional GRU classifier head"""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_classes: int, 
                 num_layers: int = 2, dropout: float = 0.3):
        super().__init__()
        
        self.hidden_dim = hidden_dim
        self.num_layers = num_layers
        
        # Bidirectional GRU
        self.gru = nn.GRU(
            input_dim, hidden_dim, num_layers,
            batch_first=True, bidirectional=True, dropout=dropout
        )
        
        # Classification head
        self.classifier = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),  # *2 for bidirectional
            nn.ReLU(inplace=True),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim, num_classes)
        )
        
        # Feature extraction layer (for prototypical loss)
        self.feature_extractor = nn.Sequential(
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.ReLU(inplace=True),
            nn.Dropout(dropout)
        )
    
    def forward(self, x: torch.Tensor, return_features: bool = False) -> torch.Tensor:
        """
        Args:
            x: Input features (B, T, input_dim)
            return_features: Whether to return intermediate features
        Returns:
            logits: Class predictions (B, num_classes)
            features: Feature embeddings (B, hidden_dim) - if return_features=True
        """
        # GRU forward pass
        gru_out, _ = self.gru(x)  # (B, T, hidden_dim * 2)
        
        # Global average pooling over time dimension
        pooled = gru_out.mean(dim=1)  # (B, hidden_dim * 2)
        
        # Extract features for prototypical loss
        features = self.feature_extractor(pooled)  # (B, hidden_dim)
        
        # Classification
        logits = self.classifier(pooled)  # (B, num_classes)
        
        if return_features:
            return logits, features
        return logits

class LipNet(nn.Module):
    """Complete LipNet model for phrase classification"""
    
    def __init__(self, num_classes: int, input_channels: int = 1, 
                 hidden_dim: int = 256, dropout: float = 0.3):
        super().__init__()
        
        self.num_classes = num_classes
        
        # CNN encoder
        self.encoder = LipNetEncoder(input_channels, dropout)
        
        # RNN classifier
        self.classifier = BiGRUClassifier(
            self.encoder.feature_dim, hidden_dim, num_classes, dropout=dropout
        )
    
    def forward(self, x: torch.Tensor, return_features: bool = False):
        """
        Args:
            x: Input video tensor (B, C, T, H, W)
            return_features: Whether to return intermediate features
        Returns:
            logits: Class predictions (B, num_classes)
            features: Feature embeddings (B, hidden_dim) - if return_features=True
        """
        # Extract spatiotemporal features
        features = self.encoder(x)  # (B, T, feature_dim)
        
        # Classify
        if return_features:
            logits, embeddings = self.classifier(features, return_features=True)
            return logits, embeddings
        else:
            logits = self.classifier(features, return_features=False)
            return logits
    
    def get_num_parameters(self) -> int:
        """Get total number of trainable parameters"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

class LipNetWithPretraining(nn.Module):
    """LipNet with pretrained backbone support"""
    
    def __init__(self, num_classes: int, pretrained_path: Optional[str] = None,
                 freeze_encoder: bool = False, **kwargs):
        super().__init__()
        
        self.lipnet = LipNet(num_classes, **kwargs)
        
        # Load pretrained weights if provided
        if pretrained_path is not None:
            self.load_pretrained(pretrained_path, freeze_encoder)
    
    def load_pretrained(self, pretrained_path: str, freeze_encoder: bool = False):
        """Load pretrained weights"""
        try:
            checkpoint = torch.load(pretrained_path, map_location='cpu')
            
            # Handle different checkpoint formats
            if 'model_state_dict' in checkpoint:
                state_dict = checkpoint['model_state_dict']
            elif 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            else:
                state_dict = checkpoint
            
            # Load encoder weights
            encoder_state_dict = {}
            for key, value in state_dict.items():
                if key.startswith('encoder.'):
                    encoder_state_dict[key] = value
            
            if encoder_state_dict:
                self.lipnet.encoder.load_state_dict(encoder_state_dict, strict=False)
                print(f"✅ Loaded pretrained encoder from {pretrained_path}")
                
                if freeze_encoder:
                    for param in self.lipnet.encoder.parameters():
                        param.requires_grad = False
                    print("🔒 Frozen encoder parameters")
            else:
                print(f"⚠️  No encoder weights found in {pretrained_path}")
                
        except Exception as e:
            print(f"❌ Error loading pretrained weights: {e}")
    
    def forward(self, x: torch.Tensor, return_features: bool = False):
        return self.lipnet(x, return_features)
    
    def get_num_parameters(self) -> int:
        return self.lipnet.get_num_parameters()

def create_model(config: dict) -> nn.Module:
    """Factory function to create LipNet model"""
    model_config = config.get('model', {})
    
    model = LipNetWithPretraining(
        num_classes=config['num_classes'],
        pretrained_path=model_config.get('pretrained_path'),
        freeze_encoder=model_config.get('freeze_encoder', False),
        hidden_dim=model_config.get('hidden_dim', 256),
        dropout=config.get('regularization', {}).get('dropout', 0.3)
    )
    
    print(f"🧠 Created LipNet with {model.get_num_parameters():,} parameters")
    
    return model
