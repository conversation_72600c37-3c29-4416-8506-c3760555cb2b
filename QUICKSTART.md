# 🚀 ICU Lipreading Project - Quick Start Guide

## 📋 Prerequisites

1. **Python 3.8+** with pip
2. **AWS CLI** configured with credentials
3. **Git** for version control
4. **CUDA** (optional, for GPU training)

## ⚡ 5-Minute Setup

### 1. <PERSON><PERSON> and Install
```bash
git clone https://github.com/jasonhall1985/LRP_final_5.10.25.git
cd LRP_final_5.10.25
pip install -r requirements.txt
```

### 2. Configure AWS (for S3 sync)
```bash
aws configure
# Enter your AWS credentials when prompted
```

### 3. Sync Data
```bash
# Automatic sync (detects if you're on local Mac or AWS SageMaker)
./sync_workflow.sh auto

# Check sync status
./sync_workflow.sh status
```

## 🎯 Common Workflows

### Data Preparation
```bash
# 1. Crop mouth ROI from raw videos
python scripts/crop_roi.py --input data/raw --output data/processed

# 2. Preprocess for training
python scripts/preprocess_videos.py \
    --input data/processed \
    --output data/processed \
    --manifest data/manifests/dataset_manifest.json

# 3. Apply augmentation
python scripts/augment_videos.py \
    --input data/processed \
    --output data/processed \
    --manifest data/manifests/dataset_manifest.json
```

### Training
```bash
# Quick training run
python scripts/train.py \
    --train-manifest data/splits/train_manifest.json \
    --val-manifest data/splits/val_manifest.json \
    --epochs 50 \
    --batch-size 16

# Full training with monitoring
python scripts/train.py \
    --train-manifest data/splits/train_manifest.json \
    --val-manifest data/splits/val_manifest.json \
    --wandb
```

### Inference
```bash
# Test single video
python scripts/inference.py \
    --checkpoint models/checkpoints/best_model.pth \
    --video path/to/test_video.mp4

# Start web server for frontend
python scripts/inference.py \
    --checkpoint models/checkpoints/best_model.pth \
    --server --port 8000
```

## 🔧 Configuration

Edit `config/system_config.yaml` to customize:
- Batch size and learning rate
- Video preprocessing parameters  
- Augmentation settings
- Model architecture options

## 📊 Monitoring

- **Local**: Check `logs/` directory for training logs
- **Cloud**: Use Weights & Biases dashboard (if enabled)
- **Results**: Evaluation metrics saved to `results/`

## 🆘 Troubleshooting

### Common Issues

**"AWS credentials not configured"**
```bash
aws configure
# Enter your access key, secret key, region
```

**"CUDA out of memory"**
- Reduce batch size in config: `batch_size: 16` → `batch_size: 8`
- Or train on CPU: `device: "cpu"`

**"Video file not found"**
- Run sync first: `./sync_workflow.sh auto`
- Check data directory: `ls data/raw/`

**"Model checkpoint not found"**
- Train model first or download pretrained weights
- Check path in inference command

### Getting Help

1. Check the full [README.md](README.md) for detailed documentation
2. Review configuration in `config/system_config.yaml`
3. Check logs in `logs/` directory
4. Open GitHub issue for bugs

## 🎉 Success Metrics

You'll know it's working when you see:
- ✅ Data sync completes without errors
- ✅ Training loss decreases over epochs  
- ✅ Validation accuracy > 80%
- ✅ Inference server responds to requests
- ✅ Frontend displays predictions

## 🔄 Next Steps

1. **Collect Data**: Add your ICU phrase videos to `data/raw/`
2. **Train Model**: Run full training pipeline
3. **Deploy**: Set up inference server for production
4. **Integrate**: Connect React frontend to prediction API
5. **Monitor**: Track model performance in real ICU environment

---

**Ready to help ICU patients communicate! 🏥💬**
