# ICU Lipreading Project (LRP_final)

A complete machine learning pipeline for enabling voiceless ICU patients to communicate visually with healthcare providers through lip-reading technology.

## 🎯 Project Overview

This project rebuilds an **Intensive Care Unit lip-reading system** modeled on SRAVI, allowing patients to silently mouth ICU phrases into a camera and receive real-time speech/text output. The system combines:

- **Frontend Interface**: React-based camera interface (from Replit)
- **ML Pipeline**: LipNet-based deep learning model for phrase classification
- **Cloud Integration**: AWS S3 + SageMaker for training and deployment
- **Local Development**: macOS Finder integration for visual data curation

## 📁 Project Structure

```
├── data/
│   ├── raw/                  # Raw video files (manually curated)
│   ├── processed/            # Preprocessed videos (96x64, 24f @15fps)
│   ├── manifests/            # Dataset manifests and splits
│   └── splits/               # Train/val/test splits (speaker-disjoint)
├── scripts/
│   ├── crop_roi.py          # ROI cropping from videos
│   ├── preprocess_videos.py # Video preprocessing pipeline
│   ├── augment_videos.py    # Data augmentation
│   ├── train.py             # Model training
│   ├── evaluate.py          # Model evaluation
│   └── inference.py         # Real-time inference
├── models/
│   ├── lipnet.py            # LipNet architecture
│   ├── pretrained/          # Pretrained model weights
│   └── checkpoints/         # Training checkpoints
├── utils/
│   ├── config.py            # Configuration management
│   ├── dataset_loader.py    # Dataset loading utilities
│   ├── metrics.py           # Evaluation metrics
│   └── losses.py            # Loss functions
├── interface/
│   └── src/                 # React frontend (from Replit)
├── notebooks/
│   ├── exploratory.ipynb    # Data exploration
│   └── error_analysis.ipynb # Model analysis
├── config/
│   └── system_config.yaml   # System configuration
└── sync_workflow.sh         # Local ↔ Cloud sync automation
```

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Clone repository
git clone https://github.com/jasonhall1985/LRP_final_5.10.25.git
cd LRP_final_5.10.25

# Install dependencies
pip install torch torchvision torchaudio
pip install opencv-python dlib scikit-learn
pip install numpy pandas matplotlib seaborn
pip install tqdm wandb flask pyyaml

# Configure AWS CLI (for S3 sync)
aws configure
```

### 2. Data Preparation

```bash
# Sync data from S3 (automatic environment detection)
./sync_workflow.sh auto

# Or manually crop ROI from raw videos
python scripts/crop_roi.py --input data/raw --output data/processed

# Preprocess videos for training
python scripts/preprocess_videos.py \
    --input data/processed \
    --output data/processed \
    --manifest data/manifests/dataset_manifest.json

# Apply data augmentation
python scripts/augment_videos.py \
    --input data/processed \
    --output data/processed \
    --manifest data/manifests/dataset_manifest.json \
    --factor 2
```

### 3. Training

```bash
# Train model with default configuration
python scripts/train.py \
    --train-manifest data/splits/train_manifest.json \
    --val-manifest data/splits/val_manifest.json \
    --output-dir models/checkpoints \
    --wandb

# Resume from checkpoint
python scripts/train.py \
    --train-manifest data/splits/train_manifest.json \
    --val-manifest data/splits/val_manifest.json \
    --resume models/checkpoints/best_model.pth
```

### 4. Evaluation

```bash
# Evaluate on test set
python scripts/evaluate.py \
    --checkpoint models/checkpoints/best_model.pth \
    --manifest data/splits/test_manifest.json \
    --output results/evaluation.json \
    --tta

# Cross-validation evaluation
python scripts/evaluate.py \
    --checkpoint models/checkpoints/best_model.pth \
    --manifest data/manifests/dataset_manifest.json \
    --cv --cv-folds 5
```

### 5. Inference

```bash
# Single video prediction
python scripts/inference.py \
    --checkpoint models/checkpoints/best_model.pth \
    --video path/to/video.mp4

# Batch prediction
python scripts/inference.py \
    --checkpoint models/checkpoints/best_model.pth \
    --batch data/test_videos/ \
    --output results/predictions.json

# Start prediction server
python scripts/inference.py \
    --checkpoint models/checkpoints/best_model.pth \
    --server --port 8000
```

## 🔄 Local ↔ Cloud Workflow

The project includes automatic synchronization between local macOS and AWS SageMaker:

```bash
# Automatic sync (detects environment)
./sync_workflow.sh auto

# Force upload to S3 (from local)
./sync_workflow.sh upload

# Force download from S3 (to local/cloud)
./sync_workflow.sh download

# Check sync status
./sync_workflow.sh status
```

**S3 Bucket**: `s3://icudatasetphrasesfortesting/speaker_sets_5.10.25/`

## 🧠 Model Architecture

- **Encoder**: 3D CNN for spatiotemporal feature extraction
- **Classifier**: Bidirectional GRU with attention
- **Input**: 96×64 grayscale videos, 24 frames @ 15 FPS
- **Output**: ICU phrase classification with confidence scores

### Training Recipe (SRAVI-based)

- **Preprocessing**: ROI crop, z-normalization, histogram matching
- **Augmentation**: Scale/shift (±10%), brightness/contrast/gamma, temporal crop, mixup
- **Loss**: Cross-entropy + Prototypical loss, label smoothing 0.05
- **Optimizer**: AdamW (lr=3e-4, bs=32), cosine decay
- **Regularization**: Dropout 0.3, gradient clipping
- **Early Stopping**: Macro-F1 ≥ 0.80, patience=10
- **Validation**: 5-fold LOSO cross-validation

## 🎯 Performance Targets

- **Validation Accuracy**: ≥ 82%
- **Macro-F1 Score**: ≥ 0.80
- **Inference Speed**: < 500ms per video
- **Confidence Threshold**: 0.45 (abstain if below)

## 🌐 Frontend Integration

The React frontend (from Replit) provides:
- Real-time camera feed
- Record button for phrase capture
- Live prediction display with confidence
- Speech synthesis output

Connect to the inference server at `/predict` endpoint.

## 📊 Monitoring & Logging

- **Weights & Biases**: Training metrics and model monitoring
- **Confusion Matrices**: Per-class performance analysis
- **Cross-Validation**: Speaker-disjoint validation
- **Error Analysis**: Jupyter notebooks for model debugging

## 🔧 Configuration

All settings are managed through `config/system_config.yaml`:

```yaml
paths:
  local_root: "/Users/<USER>/Desktop/LRP final"
  cloud_root: "/home/<USER>/SageMaker/LRP_final_5.10.25"
  s3_bucket: "s3://icudatasetphrasesfortesting/speaker_sets_5.10.25"

training:
  device: "cuda"
  batch_size: 32
  learning_rate: 3e-4
  num_epochs: 100
  early_stop_threshold: 0.80

preprocessing:
  roi_size: [96, 64]
  num_frames: 24
  fps: 15
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make changes and test thoroughly
4. Submit a pull request

## 📝 License

This project is for research and educational purposes in healthcare technology.

## 🆘 Support

For issues and questions:
- GitHub Issues: [Repository Issues](https://github.com/jasonhall1985/LRP_final_5.10.25/issues)
- Email: <EMAIL>

---

**🎉 Ready to help ICU patients communicate better through AI-powered lip-reading!**
