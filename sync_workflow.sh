#!/bin/bash

# Sync Workflow Script for ICU Lipreading Project
# Automatically detects environment and syncs data between local Mac and AWS S3

set -e  # Exit on any error

# Configuration
S3_BUCKET="s3://icudatasetphrasesfortesting/speaker_sets_5.10.25"
LOCAL_ROOT="/Users/<USER>/Desktop/LRP final"
CLOUD_ROOT="/home/<USER>/SageMaker/LRP_final_5.10.25"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Environment detection
detect_environment() {
    if [[ -d "/home/<USER>" ]]; then
        echo "cloud"
    else
        echo "local"
    fi
}

# Check AWS CLI
check_aws_cli() {
    if ! command -v aws &> /dev/null; then
        error "AWS CLI not found. Please install AWS CLI first."
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        error "AWS credentials not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    success "AWS CLI configured and ready"
}

# Sync from local to S3 (upload)
sync_local_to_s3() {
    local data_dir="$1"
    
    log "🔄 Syncing from local to S3..."
    log "Source: $data_dir"
    log "Destination: $S3_BUCKET"
    
    if [[ ! -d "$data_dir" ]]; then
        error "Local data directory not found: $data_dir"
        exit 1
    fi
    
    # Count files before sync
    local_files=$(find "$data_dir" -type f | wc -l)
    log "Found $local_files files to sync"
    
    # Perform sync with progress
    aws s3 sync "$data_dir" "$S3_BUCKET" \
        --exact-timestamps \
        --delete \
        --exclude "*.DS_Store" \
        --exclude "*.tmp" \
        --exclude ".*" \
        --cli-read-timeout 0 \
        --cli-write-timeout 0
    
    if [[ $? -eq 0 ]]; then
        success "✅ Successfully synced to S3"
        
        # Verify sync
        s3_files=$(aws s3 ls "$S3_BUCKET" --recursive | wc -l)
        log "S3 now contains $s3_files files"
    else
        error "❌ Failed to sync to S3"
        exit 1
    fi
}

# Sync from S3 to cloud (download)
sync_s3_to_cloud() {
    local data_dir="$1"
    
    log "🔄 Syncing from S3 to cloud..."
    log "Source: $S3_BUCKET"
    log "Destination: $data_dir"
    
    # Create directory if it doesn't exist
    mkdir -p "$data_dir"
    
    # Count files in S3
    s3_files=$(aws s3 ls "$S3_BUCKET" --recursive | wc -l)
    log "Found $s3_files files in S3"
    
    # Perform sync with progress
    aws s3 sync "$S3_BUCKET" "$data_dir" \
        --exact-timestamps \
        --delete \
        --cli-read-timeout 0 \
        --cli-write-timeout 0
    
    if [[ $? -eq 0 ]]; then
        success "✅ Successfully synced from S3"
        
        # Verify sync
        local_files=$(find "$data_dir" -type f | wc -l)
        log "Local directory now contains $local_files files"
    else
        error "❌ Failed to sync from S3"
        exit 1
    fi
}

# Update manifest files
update_manifests() {
    local root_dir="$1"
    local manifest_dir="$root_dir/data/manifests"
    
    log "📋 Updating manifest files..."
    
    mkdir -p "$manifest_dir"
    
    # Create basic manifest from directory structure
    python3 -c "
import os
import json
from pathlib import Path

root = Path('$root_dir')
data_dir = root / 'data' / 'raw'
manifest_dir = root / 'data' / 'manifests'

if not data_dir.exists():
    print('Data directory not found, skipping manifest update')
    exit(0)

manifest_data = []
video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv'}

for video_file in data_dir.rglob('*'):
    if video_file.suffix.lower() in video_extensions:
        # Extract speaker and phrase from path
        parts = video_file.relative_to(data_dir).parts
        if len(parts) >= 2:
            speaker_id = parts[0]
            phrase_id = parts[1] if len(parts) > 2 else parts[1].split('.')[0]
        else:
            speaker_id = 'unknown'
            phrase_id = video_file.stem
        
        manifest_data.append({
            'video_path': str(video_file),
            'speaker_id': speaker_id,
            'phrase_id': phrase_id,
            'label': phrase_id
        })

# Save manifest
manifest_path = manifest_dir / 'dataset_manifest.json'
with open(manifest_path, 'w') as f:
    json.dump(manifest_data, f, indent=2)

print(f'Created manifest with {len(manifest_data)} entries: {manifest_path}')
"
    
    success "📋 Manifest files updated"
}

# Main sync function
main_sync() {
    local direction="$1"
    local env=$(detect_environment)
    
    log "🌍 Detected environment: $env"
    
    # Check AWS CLI
    check_aws_cli
    
    if [[ "$env" == "local" ]]; then
        local root_dir="$LOCAL_ROOT"
        local data_dir="$root_dir/data/raw"
        
        if [[ "$direction" == "upload" || "$direction" == "auto" ]]; then
            log "📤 Local → S3 sync requested"
            sync_local_to_s3 "$data_dir"
            update_manifests "$root_dir"
        elif [[ "$direction" == "download" ]]; then
            log "📥 S3 → Local sync requested"
            sync_s3_to_cloud "$data_dir"
            update_manifests "$root_dir"
        fi
        
    elif [[ "$env" == "cloud" ]]; then
        local root_dir="$CLOUD_ROOT"
        local data_dir="$root_dir/data/raw"
        
        if [[ "$direction" == "download" || "$direction" == "auto" ]]; then
            log "📥 S3 → Cloud sync requested"
            sync_s3_to_cloud "$data_dir"
            update_manifests "$root_dir"
        elif [[ "$direction" == "upload" ]]; then
            log "📤 Cloud → S3 sync requested"
            sync_local_to_s3 "$data_dir"
            update_manifests "$root_dir"
        fi
    fi
}

# Status check
check_status() {
    local env=$(detect_environment)
    local root_dir
    
    if [[ "$env" == "local" ]]; then
        root_dir="$LOCAL_ROOT"
    else
        root_dir="$CLOUD_ROOT"
    fi
    
    log "📊 Environment Status:"
    echo "  Environment: $env"
    echo "  Root directory: $root_dir"
    echo "  S3 bucket: $S3_BUCKET"
    
    # Check local files
    local data_dir="$root_dir/data/raw"
    if [[ -d "$data_dir" ]]; then
        local_files=$(find "$data_dir" -type f | wc -l)
        echo "  Local files: $local_files"
    else
        echo "  Local files: 0 (directory not found)"
    fi
    
    # Check S3 files
    if command -v aws &> /dev/null && aws sts get-caller-identity &> /dev/null; then
        s3_files=$(aws s3 ls "$S3_BUCKET" --recursive 2>/dev/null | wc -l || echo "0")
        echo "  S3 files: $s3_files"
    else
        echo "  S3 files: Unable to check (AWS not configured)"
    fi
}

# Help function
show_help() {
    echo "ICU Lipreading Project - Sync Workflow Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  auto      - Automatic sync based on environment"
    echo "              Local: upload to S3, Cloud: download from S3"
    echo "  upload    - Upload local data to S3"
    echo "  download  - Download S3 data to local"
    echo "  status    - Show current sync status"
    echo "  help      - Show this help message"
    echo ""
    echo "Environment Detection:"
    echo "  Local:  macOS environment (/Users/<USER>/Desktop/LRP final)"
    echo "  Cloud:  AWS SageMaker environment (/home/<USER>/SageMaker/LRP_final_5.10.25)"
    echo ""
    echo "S3 Bucket: $S3_BUCKET"
    echo ""
    echo "Examples:"
    echo "  $0 auto      # Automatic sync"
    echo "  $0 upload    # Force upload to S3"
    echo "  $0 download  # Force download from S3"
    echo "  $0 status    # Check sync status"
}

# Main script logic
case "${1:-auto}" in
    "auto")
        main_sync "auto"
        ;;
    "upload")
        main_sync "upload"
        ;;
    "download")
        main_sync "download"
        ;;
    "status")
        check_status
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac

success "🎉 Sync workflow completed successfully!"
