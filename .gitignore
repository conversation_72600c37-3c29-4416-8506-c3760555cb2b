# ICU Lipreading Project - Git Ignore File

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyTorch
*.pth
*.pt
*.ckpt

# Data files (too large for git)
data/raw/
data/processed/
*.mp4
*.avi
*.mov
*.mkv
*.wmv
*.npy
*.npz

# Model checkpoints (too large for git)
models/checkpoints/
models/pretrained/*.pth
models/pretrained/*.pt

# Results and logs
results/
logs/
wandb/
*.log

# Jupyter Notebook
.ipynb_checkpoints

# Environment variables
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon?
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# Temporary files
*.tmp
*.temp
*.bak
*.backup
*.orig

# Compressed files
*.zip
*.tar.gz
*.rar
*.7z

# AWS
.aws/

# Node.js (for frontend)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.env.local
.env.development.local
.env.test.local
.env.production.local

# React build
interface/build/
interface/dist/

# Keep important files
!data/manifests/
!data/splits/
!models/pretrained/.gitkeep
!models/checkpoints/.gitkeep
!results/.gitkeep
!logs/.gitkeep

# Configuration files (keep templates)
!config/system_config.yaml

# Documentation
!README.md
!docs/
