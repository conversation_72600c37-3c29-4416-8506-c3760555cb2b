"""
Metrics for ICU Lipreading Project
Implements evaluation metrics including macro-F1, accuracy, and confusion matrix
"""

import numpy as np
import torch
import torch.nn.functional as F
from sklearn.metrics import (
    accuracy_score, precision_recall_fscore_support, 
    confusion_matrix, classification_report
)
from typing import List, Dict, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns

class MetricsCalculator:
    """Calculates and tracks various metrics for lipreading evaluation"""
    
    def __init__(self, class_names: List[str]):
        self.class_names = class_names
        self.num_classes = len(class_names)
        self.reset()
    
    def reset(self):
        """Reset all accumulated metrics"""
        self.all_predictions = []
        self.all_targets = []
        self.all_confidences = []
    
    def update(self, predictions: torch.Tensor, targets: torch.Tensor, 
               confidences: Optional[torch.Tensor] = None):
        """
        Update metrics with new batch
        
        Args:
            predictions: Predicted class indices (B,)
            targets: True class indices (B,)
            confidences: Prediction confidences (B,) - optional
        """
        if isinstance(predictions, torch.Tensor):
            predictions = predictions.cpu().numpy()
        if isinstance(targets, torch.Tensor):
            targets = targets.cpu().numpy()
        if confidences is not None and isinstance(confidences, torch.Tensor):
            confidences = confidences.cpu().numpy()
        
        self.all_predictions.extend(predictions.tolist())
        self.all_targets.extend(targets.tolist())
        
        if confidences is not None:
            self.all_confidences.extend(confidences.tolist())
    
    def compute_accuracy(self) -> float:
        """Compute overall accuracy"""
        return accuracy_score(self.all_targets, self.all_predictions)
    
    def compute_precision_recall_f1(self) -> Dict[str, float]:
        """Compute precision, recall, and F1 scores"""
        precision, recall, f1, support = precision_recall_fscore_support(
            self.all_targets, self.all_predictions, average=None, zero_division=0
        )
        
        # Macro averages
        macro_precision = np.mean(precision)
        macro_recall = np.mean(recall)
        macro_f1 = np.mean(f1)
        
        # Weighted averages
        weighted_precision, weighted_recall, weighted_f1, _ = precision_recall_fscore_support(
            self.all_targets, self.all_predictions, average='weighted', zero_division=0
        )
        
        return {
            'macro_precision': macro_precision,
            'macro_recall': macro_recall,
            'macro_f1': macro_f1,
            'weighted_precision': weighted_precision,
            'weighted_recall': weighted_recall,
            'weighted_f1': weighted_f1,
            'per_class_precision': precision.tolist(),
            'per_class_recall': recall.tolist(),
            'per_class_f1': f1.tolist(),
            'per_class_support': support.tolist()
        }
    
    def compute_confusion_matrix(self) -> np.ndarray:
        """Compute confusion matrix"""
        return confusion_matrix(self.all_targets, self.all_predictions)
    
    def compute_top_k_accuracy(self, logits: torch.Tensor, targets: torch.Tensor, k: int = 3) -> float:
        """Compute top-k accuracy"""
        with torch.no_grad():
            batch_size = targets.size(0)
            _, pred = logits.topk(k, 1, True, True)
            pred = pred.t()
            correct = pred.eq(targets.view(1, -1).expand_as(pred))
            correct_k = correct[:k].reshape(-1).float().sum(0, keepdim=True)
            return correct_k.mul_(100.0 / batch_size).item()
    
    def compute_confidence_metrics(self, threshold: float = 0.5) -> Dict[str, float]:
        """Compute confidence-based metrics"""
        if not self.all_confidences:
            return {}
        
        confidences = np.array(self.all_confidences)
        predictions = np.array(self.all_predictions)
        targets = np.array(self.all_targets)
        
        # High confidence predictions
        high_conf_mask = confidences >= threshold
        high_conf_acc = accuracy_score(
            targets[high_conf_mask], 
            predictions[high_conf_mask]
        ) if high_conf_mask.sum() > 0 else 0.0
        
        # Coverage (percentage of samples above threshold)
        coverage = high_conf_mask.mean()
        
        return {
            'high_confidence_accuracy': high_conf_acc,
            'coverage': coverage,
            'mean_confidence': confidences.mean(),
            'std_confidence': confidences.std()
        }
    
    def get_classification_report(self) -> str:
        """Get detailed classification report"""
        return classification_report(
            self.all_targets, 
            self.all_predictions, 
            target_names=self.class_names,
            zero_division=0
        )
    
    def plot_confusion_matrix(self, save_path: Optional[str] = None, normalize: bool = True) -> plt.Figure:
        """Plot confusion matrix"""
        cm = self.compute_confusion_matrix()
        
        if normalize:
            cm = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
            fmt = '.2f'
            title = 'Normalized Confusion Matrix'
        else:
            fmt = 'd'
            title = 'Confusion Matrix'
        
        fig, ax = plt.subplots(figsize=(10, 8))
        sns.heatmap(cm, annot=True, fmt=fmt, cmap='Blues', 
                   xticklabels=self.class_names, 
                   yticklabels=self.class_names, ax=ax)
        
        ax.set_title(title)
        ax.set_ylabel('True Label')
        ax.set_xlabel('Predicted Label')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def compute_all_metrics(self) -> Dict[str, float]:
        """Compute all available metrics"""
        metrics = {}
        
        # Basic accuracy
        metrics['accuracy'] = self.compute_accuracy()
        
        # Precision, recall, F1
        prf_metrics = self.compute_precision_recall_f1()
        metrics.update(prf_metrics)
        
        # Confidence metrics
        conf_metrics = self.compute_confidence_metrics()
        metrics.update(conf_metrics)
        
        return metrics

class EarlyStopping:
    """Early stopping utility"""
    
    def __init__(self, patience: int = 10, min_delta: float = 0.001, 
                 mode: str = 'max', restore_best_weights: bool = True):
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.restore_best_weights = restore_best_weights
        
        self.best_score = None
        self.counter = 0
        self.best_weights = None
        
        if mode == 'max':
            self.is_better = lambda score, best: score > best + min_delta
        else:
            self.is_better = lambda score, best: score < best - min_delta
    
    def __call__(self, score: float, model: torch.nn.Module) -> bool:
        """
        Check if training should stop
        
        Returns:
            True if training should stop, False otherwise
        """
        if self.best_score is None:
            self.best_score = score
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            return False
        
        if self.is_better(score, self.best_score):
            self.best_score = score
            self.counter = 0
            if self.restore_best_weights:
                self.best_weights = model.state_dict().copy()
            return False
        else:
            self.counter += 1
            if self.counter >= self.patience:
                if self.restore_best_weights and self.best_weights is not None:
                    model.load_state_dict(self.best_weights)
                return True
            return False

def compute_class_weights(targets: List[int], num_classes: int) -> torch.Tensor:
    """Compute class weights for imbalanced datasets"""
    class_counts = np.bincount(targets, minlength=num_classes)
    total_samples = len(targets)
    
    # Inverse frequency weighting
    class_weights = total_samples / (num_classes * class_counts + 1e-6)
    
    return torch.FloatTensor(class_weights)

def mixup_criterion(criterion, pred, y_a, y_b, lam):
    """Mixup loss function"""
    return lam * criterion(pred, y_a) + (1 - lam) * criterion(pred, y_b)

def label_smoothing_loss(pred, target, smoothing=0.1):
    """Label smoothing cross entropy loss"""
    confidence = 1.0 - smoothing
    log_probs = F.log_softmax(pred, dim=-1)
    nll_loss = -log_probs.gather(dim=-1, index=target.unsqueeze(1))
    nll_loss = nll_loss.squeeze(1)
    smooth_loss = -log_probs.mean(dim=-1)
    loss = confidence * nll_loss + smoothing * smooth_loss
    return loss.mean()
