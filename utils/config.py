"""
Configuration management for ICU Lipreading Project
Handles automatic environment detection (local macOS vs AWS SageMaker)
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional

class Config:
    """Configuration manager with automatic environment detection"""
    
    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            # Try to find config file relative to this script
            current_dir = Path(__file__).parent.parent
            config_path = current_dir / "config" / "system_config.yaml"
        
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.root_path = self._detect_environment()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        try:
            with open(self.config_path, 'r') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing configuration file: {e}")
    
    def _detect_environment(self) -> Path:
        """Detect if running on local macOS or AWS SageMaker"""
        cloud_indicator = "/home/<USER>"
        
        if os.path.exists(cloud_indicator):
            print("🌩️  Detected AWS SageMaker environment")
            return Path(self.config["paths"]["cloud_root"])
        else:
            print("🖥️  Detected local macOS environment")
            return Path(self.config["paths"]["local_root"])
    
    def get_path(self, key: str) -> Path:
        """Get absolute path for a given key"""
        relative_path = self.config["data"].get(key)
        if relative_path is None:
            raise KeyError(f"Path key '{key}' not found in configuration")
        return self.root_path / relative_path
    
    def get_s3_bucket(self) -> str:
        """Get S3 bucket path"""
        return self.config["paths"]["s3_bucket"]
    
    def get_training_config(self) -> Dict[str, Any]:
        """Get training configuration"""
        return self.config["training"]
    
    def get_preprocessing_config(self) -> Dict[str, Any]:
        """Get preprocessing configuration"""
        return self.config["preprocessing"]
    
    def get_augmentation_config(self) -> Dict[str, Any]:
        """Get augmentation configuration"""
        return self.config["augmentation"]
    
    def get_inference_config(self) -> Dict[str, Any]:
        """Get inference configuration"""
        return self.config["inference"]
    
    def get_validation_config(self) -> Dict[str, Any]:
        """Get validation configuration"""
        return self.config["validation"]
    
    def is_cloud_environment(self) -> bool:
        """Check if running in cloud environment"""
        return os.path.exists("/home/<USER>")
    
    def is_local_environment(self) -> bool:
        """Check if running in local environment"""
        return not self.is_cloud_environment()
    
    def __getitem__(self, key: str) -> Any:
        """Allow dictionary-style access to config"""
        return self.config[key]
    
    def __repr__(self) -> str:
        env = "Cloud" if self.is_cloud_environment() else "Local"
        return f"Config(environment={env}, root={self.root_path})"

# Global config instance
config = Config()

# Convenience functions
def get_config() -> Config:
    """Get global configuration instance"""
    return config

def get_root_path() -> Path:
    """Get root path for current environment"""
    return config.root_path

def get_data_path(key: str) -> Path:
    """Get data path for given key"""
    return config.get_path(key)
