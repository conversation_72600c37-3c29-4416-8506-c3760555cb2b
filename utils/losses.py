"""
Loss Functions for ICU Lipreading Project
Implements various loss functions including prototypical loss and label smoothing
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Tuple, Optional

class PrototypicalLoss(nn.Module):
    """
    Prototypical Loss for few-shot learning
    Computes distance to class prototypes
    """
    
    def __init__(self, distance_metric: str = 'euclidean'):
        super().__init__()
        self.distance_metric = distance_metric
    
    def forward(self, embeddings: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            embeddings: Feature embeddings (B, D)
            targets: Class labels (B,)
        """
        unique_labels = torch.unique(targets)
        num_classes = len(unique_labels)
        
        if num_classes == 1:
            # Only one class in batch, return zero loss
            return torch.tensor(0.0, device=embeddings.device, requires_grad=True)
        
        # Compute class prototypes
        prototypes = []
        for label in unique_labels:
            mask = targets == label
            prototype = embeddings[mask].mean(dim=0)
            prototypes.append(prototype)
        
        prototypes = torch.stack(prototypes)  # (num_classes, D)
        
        # Compute distances
        if self.distance_metric == 'euclidean':
            distances = torch.cdist(embeddings, prototypes)  # (B, num_classes)
        elif self.distance_metric == 'cosine':
            embeddings_norm = F.normalize(embeddings, dim=1)
            prototypes_norm = F.normalize(prototypes, dim=1)
            distances = 1 - torch.mm(embeddings_norm, prototypes_norm.t())
        else:
            raise ValueError(f"Unknown distance metric: {self.distance_metric}")
        
        # Convert distances to logits (negative distances)
        logits = -distances
        
        # Map original labels to prototype indices
        label_to_idx = {label.item(): idx for idx, label in enumerate(unique_labels)}
        prototype_targets = torch.tensor([label_to_idx[t.item()] for t in targets], 
                                       device=targets.device)
        
        # Compute cross-entropy loss
        loss = F.cross_entropy(logits, prototype_targets)
        
        return loss

class FocalLoss(nn.Module):
    """
    Focal Loss for addressing class imbalance
    """
    
    def __init__(self, alpha: float = 1.0, gamma: float = 2.0, reduction: str = 'mean'):
        super().__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: Logits (B, num_classes)
            targets: Class labels (B,)
        """
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        pt = torch.exp(-ce_loss)
        focal_loss = self.alpha * (1 - pt) ** self.gamma * ce_loss
        
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss

class LabelSmoothingLoss(nn.Module):
    """
    Label Smoothing Cross Entropy Loss
    """
    
    def __init__(self, num_classes: int, smoothing: float = 0.1):
        super().__init__()
        self.num_classes = num_classes
        self.smoothing = smoothing
        self.confidence = 1.0 - smoothing
    
    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            inputs: Logits (B, num_classes)
            targets: Class labels (B,)
        """
        log_probs = F.log_softmax(inputs, dim=-1)
        
        # One-hot encoding with label smoothing
        true_dist = torch.zeros_like(log_probs)
        true_dist.fill_(self.smoothing / (self.num_classes - 1))
        true_dist.scatter_(1, targets.unsqueeze(1), self.confidence)
        
        loss = torch.sum(-true_dist * log_probs, dim=-1)
        return loss.mean()

class CombinedLoss(nn.Module):
    """
    Combined loss function with multiple components
    """
    
    def __init__(self, 
                 num_classes: int,
                 ce_weight: float = 1.0,
                 prototypical_weight: float = 0.3,
                 focal_weight: float = 0.0,
                 label_smoothing: float = 0.05):
        super().__init__()
        
        self.ce_weight = ce_weight
        self.prototypical_weight = prototypical_weight
        self.focal_weight = focal_weight
        
        # Loss components
        if label_smoothing > 0:
            self.ce_loss = LabelSmoothingLoss(num_classes, label_smoothing)
        else:
            self.ce_loss = nn.CrossEntropyLoss()
        
        if prototypical_weight > 0:
            self.prototypical_loss = PrototypicalLoss()
        
        if focal_weight > 0:
            self.focal_loss = FocalLoss()
    
    def forward(self, 
                logits: torch.Tensor, 
                targets: torch.Tensor,
                embeddings: Optional[torch.Tensor] = None) -> Tuple[torch.Tensor, dict]:
        """
        Args:
            logits: Model predictions (B, num_classes)
            targets: True labels (B,)
            embeddings: Feature embeddings (B, D) - required for prototypical loss
        
        Returns:
            total_loss: Combined loss value
            loss_dict: Dictionary with individual loss components
        """
        loss_dict = {}
        total_loss = 0.0
        
        # Cross-entropy loss
        if self.ce_weight > 0:
            ce_loss = self.ce_loss(logits, targets)
            loss_dict['ce_loss'] = ce_loss.item()
            total_loss += self.ce_weight * ce_loss
        
        # Prototypical loss
        if self.prototypical_weight > 0 and embeddings is not None:
            proto_loss = self.prototypical_loss(embeddings, targets)
            loss_dict['prototypical_loss'] = proto_loss.item()
            total_loss += self.prototypical_weight * proto_loss
        
        # Focal loss
        if self.focal_weight > 0:
            focal_loss = self.focal_loss(logits, targets)
            loss_dict['focal_loss'] = focal_loss.item()
            total_loss += self.focal_weight * focal_loss
        
        loss_dict['total_loss'] = total_loss.item()
        
        return total_loss, loss_dict

class TripletLoss(nn.Module):
    """
    Triplet Loss for learning discriminative embeddings
    """
    
    def __init__(self, margin: float = 1.0, distance_metric: str = 'euclidean'):
        super().__init__()
        self.margin = margin
        self.distance_metric = distance_metric
    
    def forward(self, embeddings: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            embeddings: Feature embeddings (B, D)
            targets: Class labels (B,)
        """
        batch_size = embeddings.size(0)
        
        if self.distance_metric == 'euclidean':
            # Compute pairwise distances
            distances = torch.cdist(embeddings, embeddings)
        elif self.distance_metric == 'cosine':
            # Compute cosine distances
            embeddings_norm = F.normalize(embeddings, dim=1)
            similarities = torch.mm(embeddings_norm, embeddings_norm.t())
            distances = 1 - similarities
        else:
            raise ValueError(f"Unknown distance metric: {self.distance_metric}")
        
        # Create masks for positive and negative pairs
        targets_equal = targets.unsqueeze(0) == targets.unsqueeze(1)
        targets_not_equal = ~targets_equal
        
        # For each anchor, find hardest positive and negative
        losses = []
        
        for i in range(batch_size):
            # Positive distances (same class, excluding self)
            pos_mask = targets_equal[i] & (torch.arange(batch_size, device=embeddings.device) != i)
            if pos_mask.sum() == 0:
                continue  # No positive samples
            
            pos_distances = distances[i][pos_mask]
            hardest_positive = pos_distances.max()
            
            # Negative distances (different class)
            neg_mask = targets_not_equal[i]
            if neg_mask.sum() == 0:
                continue  # No negative samples
            
            neg_distances = distances[i][neg_mask]
            hardest_negative = neg_distances.min()
            
            # Triplet loss
            loss = F.relu(hardest_positive - hardest_negative + self.margin)
            losses.append(loss)
        
        if len(losses) == 0:
            return torch.tensor(0.0, device=embeddings.device, requires_grad=True)
        
        return torch.stack(losses).mean()

class ContrastiveLoss(nn.Module):
    """
    Contrastive Loss for learning embeddings
    """
    
    def __init__(self, margin: float = 1.0):
        super().__init__()
        self.margin = margin
    
    def forward(self, embeddings: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        Args:
            embeddings: Feature embeddings (B, D)
            targets: Class labels (B,)
        """
        batch_size = embeddings.size(0)
        
        # Compute pairwise distances
        distances = torch.cdist(embeddings, embeddings)
        
        # Create labels for pairs (1 for same class, 0 for different class)
        targets_equal = (targets.unsqueeze(0) == targets.unsqueeze(1)).float()
        
        # Contrastive loss
        pos_loss = targets_equal * distances.pow(2)
        neg_loss = (1 - targets_equal) * F.relu(self.margin - distances).pow(2)
        
        # Average over all pairs (excluding diagonal)
        mask = torch.eye(batch_size, device=embeddings.device) == 0
        loss = (pos_loss + neg_loss)[mask].mean()
        
        return loss

def get_loss_function(loss_config: dict, num_classes: int) -> nn.Module:
    """
    Factory function to create loss function based on configuration
    """
    loss_type = loss_config.get('type', 'combined')
    
    if loss_type == 'cross_entropy':
        if loss_config.get('label_smoothing', 0) > 0:
            return LabelSmoothingLoss(num_classes, loss_config['label_smoothing'])
        else:
            return nn.CrossEntropyLoss()
    
    elif loss_type == 'focal':
        return FocalLoss(
            alpha=loss_config.get('alpha', 1.0),
            gamma=loss_config.get('gamma', 2.0)
        )
    
    elif loss_type == 'prototypical':
        return PrototypicalLoss(
            distance_metric=loss_config.get('distance_metric', 'euclidean')
        )
    
    elif loss_type == 'combined':
        return CombinedLoss(
            num_classes=num_classes,
            ce_weight=loss_config.get('ce_weight', 1.0),
            prototypical_weight=loss_config.get('prototypical_weight', 0.3),
            focal_weight=loss_config.get('focal_weight', 0.0),
            label_smoothing=loss_config.get('label_smoothing', 0.05)
        )
    
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")
