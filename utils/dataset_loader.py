"""
Dataset Loader for ICU Lipreading Project
Handles loading and batching of video data with speaker-disjoint splits
"""

import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
import json
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
from sklearn.model_selection import GroupKFold
import random

class LipreadingDataset(Dataset):
    """Dataset class for lipreading videos"""
    
    def __init__(self, 
                 manifest_path: Union[str, Path],
                 split: str = 'train',
                 transform=None,
                 max_samples: Optional[int] = None):
        
        self.manifest_path = Path(manifest_path)
        self.split = split
        self.transform = transform
        
        # Load manifest
        with open(self.manifest_path, 'r') as f:
            self.manifest = json.load(f)
        
        # Filter by split if specified
        if 'split' in self.manifest[0]:
            self.manifest = [item for item in self.manifest if item.get('split') == split]
        
        # Limit samples if specified
        if max_samples is not None:
            self.manifest = self.manifest[:max_samples]
        
        # Create label mapping
        self.labels = sorted(list(set(item['label'] for item in self.manifest)))
        self.label_to_idx = {label: idx for idx, label in enumerate(self.labels)}
        self.idx_to_label = {idx: label for label, idx in self.label_to_idx.items()}
        
        print(f"📊 Loaded {len(self.manifest)} samples for {split} split")
        print(f"🏷️  Found {len(self.labels)} unique labels: {self.labels}")
    
    def __len__(self) -> int:
        return len(self.manifest)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int, Dict]:
        item = self.manifest[idx]
        
        # Load video frames
        video_path = Path(item['video_path'])
        if not video_path.exists():
            raise FileNotFoundError(f"Video file not found: {video_path}")
        
        frames = np.load(video_path)  # Shape: (T, H, W)
        
        # Convert to tensor and add channel dimension
        frames = torch.from_numpy(frames).float()
        if len(frames.shape) == 3:
            frames = frames.unsqueeze(1)  # Shape: (T, 1, H, W)
        
        # Get label index
        label_idx = self.label_to_idx[item['label']]
        
        # Apply transforms if specified
        if self.transform is not None:
            frames = self.transform(frames)
        
        # Metadata
        metadata = {
            'speaker_id': item['speaker_id'],
            'phrase_id': item['phrase_id'],
            'video_path': str(video_path),
            'label': item['label']
        }
        
        return frames, label_idx, metadata
    
    def get_speaker_ids(self) -> List[str]:
        """Get list of unique speaker IDs"""
        return sorted(list(set(item['speaker_id'] for item in self.manifest)))
    
    def get_samples_by_speaker(self, speaker_id: str) -> List[Dict]:
        """Get all samples for a specific speaker"""
        return [item for item in self.manifest if item['speaker_id'] == speaker_id]

class SpeakerDisjointSplitter:
    """Creates speaker-disjoint train/val/test splits"""
    
    def __init__(self, manifest_path: Union[str, Path], 
                 train_ratio: float = 0.7,
                 val_ratio: float = 0.15,
                 test_ratio: float = 0.15,
                 random_state: int = 42):
        
        self.manifest_path = Path(manifest_path)
        self.train_ratio = train_ratio
        self.val_ratio = val_ratio
        self.test_ratio = test_ratio
        self.random_state = random_state
        
        # Validate ratios
        if abs(train_ratio + val_ratio + test_ratio - 1.0) > 1e-6:
            raise ValueError("Split ratios must sum to 1.0")
    
    def create_splits(self, output_dir: Path) -> Dict[str, Path]:
        """Create speaker-disjoint splits and save separate manifest files"""
        
        # Load original manifest
        with open(self.manifest_path, 'r') as f:
            manifest = json.load(f)
        
        # Group by speaker
        speaker_samples = {}
        for item in manifest:
            speaker_id = item['speaker_id']
            if speaker_id not in speaker_samples:
                speaker_samples[speaker_id] = []
            speaker_samples[speaker_id].append(item)
        
        speakers = list(speaker_samples.keys())
        random.seed(self.random_state)
        random.shuffle(speakers)
        
        # Calculate split indices
        n_speakers = len(speakers)
        train_end = int(n_speakers * self.train_ratio)
        val_end = train_end + int(n_speakers * self.val_ratio)
        
        # Split speakers
        train_speakers = speakers[:train_end]
        val_speakers = speakers[train_end:val_end]
        test_speakers = speakers[val_end:]
        
        print(f"👥 Speaker distribution:")
        print(f"   Train: {len(train_speakers)} speakers")
        print(f"   Val:   {len(val_speakers)} speakers")
        print(f"   Test:  {len(test_speakers)} speakers")
        
        # Create split manifests
        splits = {
            'train': [],
            'val': [],
            'test': []
        }
        
        for speaker_id, samples in speaker_samples.items():
            if speaker_id in train_speakers:
                for sample in samples:
                    sample['split'] = 'train'
                    splits['train'].append(sample)
            elif speaker_id in val_speakers:
                for sample in samples:
                    sample['split'] = 'val'
                    splits['val'].append(sample)
            else:
                for sample in samples:
                    sample['split'] = 'test'
                    splits['test'].append(sample)
        
        # Save split manifests
        output_dir.mkdir(parents=True, exist_ok=True)
        split_paths = {}
        
        for split_name, split_data in splits.items():
            split_path = output_dir / f"{split_name}_manifest.json"
            with open(split_path, 'w') as f:
                json.dump(split_data, f, indent=2)
            split_paths[split_name] = split_path
            
            print(f"💾 Saved {split_name} split: {len(split_data)} samples -> {split_path}")
        
        return split_paths

class CrossValidationSplitter:
    """Creates speaker-disjoint cross-validation folds"""
    
    def __init__(self, manifest_path: Union[str, Path], n_folds: int = 5, random_state: int = 42):
        self.manifest_path = Path(manifest_path)
        self.n_folds = n_folds
        self.random_state = random_state
    
    def create_folds(self, output_dir: Path) -> List[Dict[str, Path]]:
        """Create cross-validation folds with speaker-disjoint splits"""
        
        # Load original manifest
        with open(self.manifest_path, 'r') as f:
            manifest = json.load(f)
        
        # Group by speaker
        speaker_samples = {}
        for item in manifest:
            speaker_id = item['speaker_id']
            if speaker_id not in speaker_samples:
                speaker_samples[speaker_id] = []
            speaker_samples[speaker_id].append(item)
        
        speakers = list(speaker_samples.keys())
        speaker_labels = [0] * len(speakers)  # Dummy labels for GroupKFold
        
        # Create folds
        gkf = GroupKFold(n_splits=self.n_folds)
        fold_paths = []
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for fold_idx, (train_idx, val_idx) in enumerate(gkf.split(speakers, speaker_labels, speakers)):
            train_speakers = [speakers[i] for i in train_idx]
            val_speakers = [speakers[i] for i in val_idx]
            
            # Create fold data
            train_data = []
            val_data = []
            
            for speaker_id, samples in speaker_samples.items():
                if speaker_id in train_speakers:
                    for sample in samples:
                        sample_copy = sample.copy()
                        sample_copy['fold'] = fold_idx
                        sample_copy['split'] = 'train'
                        train_data.append(sample_copy)
                else:
                    for sample in samples:
                        sample_copy = sample.copy()
                        sample_copy['fold'] = fold_idx
                        sample_copy['split'] = 'val'
                        val_data.append(sample_copy)
            
            # Save fold manifests
            fold_dir = output_dir / f"fold_{fold_idx}"
            fold_dir.mkdir(exist_ok=True)
            
            train_path = fold_dir / "train_manifest.json"
            val_path = fold_dir / "val_manifest.json"
            
            with open(train_path, 'w') as f:
                json.dump(train_data, f, indent=2)
            
            with open(val_path, 'w') as f:
                json.dump(val_data, f, indent=2)
            
            fold_paths.append({
                'train': train_path,
                'val': val_path,
                'fold': fold_idx
            })
            
            print(f"📁 Fold {fold_idx}: {len(train_data)} train, {len(val_data)} val samples")
        
        return fold_paths

def create_dataloader(dataset: LipreadingDataset, 
                     batch_size: int = 32,
                     shuffle: bool = True,
                     num_workers: int = 4,
                     pin_memory: bool = True) -> DataLoader:
    """Create DataLoader with appropriate settings"""
    
    return DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=shuffle,
        num_workers=num_workers,
        pin_memory=pin_memory,
        drop_last=True if shuffle else False
    )

def collate_fn(batch):
    """Custom collate function for variable-length sequences"""
    frames, labels, metadata = zip(*batch)
    
    # Stack frames and labels
    frames = torch.stack(frames, dim=0)
    labels = torch.tensor(labels, dtype=torch.long)
    
    return frames, labels, list(metadata)
