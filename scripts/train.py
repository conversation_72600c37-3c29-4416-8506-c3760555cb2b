"""
Training Script for ICU Lipreading Project
Implements complete training pipeline with SRAVI recipe
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import argparse
from pathlib import Path
import json
import sys
import os
from tqdm import tqdm
import wandb
from typing import Dict, Any, Optional

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from utils import (
    get_config, LipreadingDataset, create_dataloader,
    MetricsCalculator, EarlyStopping, get_loss_function
)
from models.lipnet import create_model

class Trainer:
    """Main training class"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = torch.device(config['training']['device'] if torch.cuda.is_available() else 'cpu')
        
        # Initialize model
        self.model = create_model(config).to(self.device)
        
        # Initialize datasets
        self.train_dataset = None
        self.val_dataset = None
        self.train_loader = None
        self.val_loader = None
        
        # Initialize training components
        self.optimizer = None
        self.scheduler = None
        self.criterion = None
        self.metrics_calculator = None
        self.early_stopping = None
        
        # Training state
        self.current_epoch = 0
        self.best_score = 0.0
        self.training_history = []
        
        print(f"🚀 Initialized trainer on device: {self.device}")
    
    def setup_data(self, train_manifest: str, val_manifest: str):
        """Setup training and validation datasets"""
        
        # Create datasets
        self.train_dataset = LipreadingDataset(train_manifest, split='train')
        self.val_dataset = LipreadingDataset(val_manifest, split='val')
        
        # Update config with dataset info
        self.config['num_classes'] = len(self.train_dataset.labels)
        self.config['class_names'] = self.train_dataset.labels
        
        # Create data loaders
        batch_size = self.config['training']['batch_size']
        self.train_loader = create_dataloader(
            self.train_dataset, batch_size=batch_size, shuffle=True
        )
        self.val_loader = create_dataloader(
            self.val_dataset, batch_size=batch_size, shuffle=False
        )
        
        print(f"📊 Training samples: {len(self.train_dataset)}")
        print(f"📊 Validation samples: {len(self.val_dataset)}")
        print(f"🏷️  Number of classes: {self.config['num_classes']}")
    
    def setup_training_components(self):
        """Setup optimizer, scheduler, loss function, and metrics"""
        
        # Optimizer
        optimizer_config = self.config.get('optimizer', {})
        if optimizer_config.get('name', 'AdamW').lower() == 'adamw':
            self.optimizer = optim.AdamW(
                self.model.parameters(),
                lr=self.config['training']['learning_rate'],
                weight_decay=optimizer_config.get('weight_decay', 1e-4)
            )
        else:
            self.optimizer = optim.Adam(
                self.model.parameters(),
                lr=self.config['training']['learning_rate']
            )
        
        # Scheduler
        scheduler_config = self.config.get('scheduler', {})
        if scheduler_config.get('name', 'cosine').lower() == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer,
                T_max=self.config['training']['num_epochs'],
                eta_min=1e-6
            )
        else:
            self.scheduler = optim.lr_scheduler.StepLR(
                self.optimizer, step_size=30, gamma=0.1
            )
        
        # Loss function
        loss_config = {
            'type': 'combined',
            'label_smoothing': self.config.get('loss', {}).get('label_smoothing', 0.05),
            'prototypical_weight': self.config.get('loss', {}).get('prototypical_weight', 0.3)
        }
        self.criterion = get_loss_function(loss_config, self.config['num_classes'])
        
        # Metrics calculator
        self.metrics_calculator = MetricsCalculator(self.config['class_names'])
        
        # Early stopping
        early_stop_config = self.config['training']
        self.early_stopping = EarlyStopping(
            patience=early_stop_config.get('early_stop_patience', 10),
            mode='max',
            min_delta=0.001
        )
        
        print("✅ Training components initialized")
    
    def train_epoch(self) -> Dict[str, float]:
        """Train for one epoch"""
        self.model.train()
        self.metrics_calculator.reset()
        
        total_loss = 0.0
        loss_components = {}
        
        pbar = tqdm(self.train_loader, desc=f"Epoch {self.current_epoch}")
        
        for batch_idx, (videos, targets, metadata) in enumerate(pbar):
            videos = videos.to(self.device)
            targets = targets.to(self.device)
            
            # Forward pass
            self.optimizer.zero_grad()
            
            if hasattr(self.criterion, 'prototypical_weight') and self.criterion.prototypical_weight > 0:
                logits, embeddings = self.model(videos, return_features=True)
                loss, loss_dict = self.criterion(logits, targets, embeddings)
            else:
                logits = self.model(videos)
                loss, loss_dict = self.criterion(logits, targets)
            
            # Backward pass
            loss.backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Update metrics
            predictions = torch.argmax(logits, dim=1)
            confidences = torch.softmax(logits, dim=1).max(dim=1)[0]
            
            self.metrics_calculator.update(predictions, targets, confidences)
            
            # Track losses
            total_loss += loss.item()
            for key, value in loss_dict.items():
                if key not in loss_components:
                    loss_components[key] = 0.0
                loss_components[key] += value
            
            # Update progress bar
            pbar.set_postfix({
                'loss': f"{loss.item():.4f}",
                'lr': f"{self.optimizer.param_groups[0]['lr']:.2e}"
            })
        
        # Calculate epoch metrics
        epoch_metrics = self.metrics_calculator.compute_all_metrics()
        epoch_metrics['loss'] = total_loss / len(self.train_loader)
        
        # Add loss components
        for key, value in loss_components.items():
            epoch_metrics[f"train_{key}"] = value / len(self.train_loader)
        
        return epoch_metrics

    def validate_epoch(self) -> Dict[str, float]:
        """Validate for one epoch"""
        self.model.eval()
        self.metrics_calculator.reset()

        total_loss = 0.0

        with torch.no_grad():
            pbar = tqdm(self.val_loader, desc="Validation")

            for videos, targets, metadata in pbar:
                videos = videos.to(self.device)
                targets = targets.to(self.device)

                # Forward pass
                if hasattr(self.criterion, 'prototypical_weight') and self.criterion.prototypical_weight > 0:
                    logits, embeddings = self.model(videos, return_features=True)
                    loss, _ = self.criterion(logits, targets, embeddings)
                else:
                    logits = self.model(videos)
                    loss, _ = self.criterion(logits, targets)

                # Update metrics
                predictions = torch.argmax(logits, dim=1)
                confidences = torch.softmax(logits, dim=1).max(dim=1)[0]

                self.metrics_calculator.update(predictions, targets, confidences)
                total_loss += loss.item()

                pbar.set_postfix({'val_loss': f"{loss.item():.4f}"})

        # Calculate validation metrics
        val_metrics = self.metrics_calculator.compute_all_metrics()
        val_metrics['val_loss'] = total_loss / len(self.val_loader)

        return val_metrics

    def train(self, num_epochs: int, save_dir: Path, use_wandb: bool = False):
        """Main training loop"""

        save_dir.mkdir(parents=True, exist_ok=True)

        # Initialize wandb if requested
        if use_wandb:
            wandb.init(project="icu-lipreading", config=self.config)
            wandb.watch(self.model)

        print(f"🎯 Starting training for {num_epochs} epochs")
        print(f"💾 Checkpoints will be saved to: {save_dir}")

        for epoch in range(num_epochs):
            self.current_epoch = epoch + 1

            # Training
            train_metrics = self.train_epoch()

            # Validation
            val_metrics = self.validate_epoch()

            # Learning rate scheduling
            self.scheduler.step()

            # Combine metrics
            epoch_metrics = {**train_metrics, **val_metrics}
            epoch_metrics['epoch'] = self.current_epoch
            epoch_metrics['lr'] = self.optimizer.param_groups[0]['lr']

            self.training_history.append(epoch_metrics)

            # Print epoch summary
            print(f"\nEpoch {self.current_epoch}/{num_epochs}:")
            print(f"  Train Loss: {train_metrics['loss']:.4f} | Train Acc: {train_metrics['accuracy']:.4f}")
            print(f"  Val Loss: {val_metrics['val_loss']:.4f} | Val Acc: {val_metrics['accuracy']:.4f}")
            print(f"  Val Macro-F1: {val_metrics['macro_f1']:.4f}")

            # Log to wandb
            if use_wandb:
                wandb.log(epoch_metrics)

            # Check for best model
            current_score = val_metrics['macro_f1']
            if current_score > self.best_score:
                self.best_score = current_score
                self.save_checkpoint(save_dir / "best_model.pth", is_best=True)
                print(f"  🎉 New best model! Macro-F1: {current_score:.4f}")

            # Save regular checkpoint
            if epoch % 10 == 0:
                self.save_checkpoint(save_dir / f"checkpoint_epoch_{epoch}.pth")

            # Early stopping check
            if self.early_stopping(current_score, self.model):
                print(f"🛑 Early stopping triggered at epoch {self.current_epoch}")
                break

        # Save final model and training history
        self.save_checkpoint(save_dir / "final_model.pth")
        self.save_training_history(save_dir / "training_history.json")

        if use_wandb:
            wandb.finish()

        print(f"✅ Training completed! Best Macro-F1: {self.best_score:.4f}")

    def save_checkpoint(self, path: Path, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_score': self.best_score,
            'config': self.config
        }

        torch.save(checkpoint, path)

        if is_best:
            print(f"💾 Saved best model checkpoint: {path}")

    def save_training_history(self, path: Path):
        """Save training history"""
        with open(path, 'w') as f:
            json.dump(self.training_history, f, indent=2)
        print(f"📊 Saved training history: {path}")

def main():
    parser = argparse.ArgumentParser(description="Train ICU Lipreading Model")
    parser.add_argument("--train-manifest", "-t", type=str, required=True, help="Training manifest file")
    parser.add_argument("--val-manifest", "-v", type=str, required=True, help="Validation manifest file")
    parser.add_argument("--config", "-c", type=str, help="Custom config file")
    parser.add_argument("--output-dir", "-o", type=str, default="models/checkpoints", help="Output directory")
    parser.add_argument("--epochs", "-e", type=int, help="Number of epochs (overrides config)")
    parser.add_argument("--batch-size", "-b", type=int, help="Batch size (overrides config)")
    parser.add_argument("--lr", type=float, help="Learning rate (overrides config)")
    parser.add_argument("--wandb", action="store_true", help="Use Weights & Biases logging")
    parser.add_argument("--resume", type=str, help="Resume from checkpoint")

    args = parser.parse_args()

    # Load configuration
    if args.config:
        import yaml
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)
    else:
        config = get_config().config

    # Override config with command line arguments
    if args.epochs:
        config['training']['num_epochs'] = args.epochs
    if args.batch_size:
        config['training']['batch_size'] = args.batch_size
    if args.lr:
        config['training']['learning_rate'] = args.lr

    # Initialize trainer
    trainer = Trainer(config)

    # Setup data
    trainer.setup_data(args.train_manifest, args.val_manifest)

    # Setup training components
    trainer.setup_training_components()

    # Resume from checkpoint if specified
    if args.resume:
        checkpoint = torch.load(args.resume, map_location=trainer.device)
        trainer.model.load_state_dict(checkpoint['model_state_dict'])
        trainer.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        trainer.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        trainer.current_epoch = checkpoint['epoch']
        trainer.best_score = checkpoint['best_score']
        print(f"📂 Resumed from checkpoint: {args.resume}")

    # Start training
    output_dir = Path(args.output_dir)
    trainer.train(
        num_epochs=config['training']['num_epochs'],
        save_dir=output_dir,
        use_wandb=args.wandb
    )

if __name__ == "__main__":
    main()
