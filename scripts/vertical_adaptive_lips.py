#!/usr/bin/env python3
"""
Vertical-Adaptive Lip Cropping for Bottom-Half Faces
----------------------------------------------------
<PERSON>les videos showing bottom half of faces where lips appear at 
different vertical positions (some higher, some lower in the frame).
"""

import os
import cv2
import numpy as np
from glob import glob

try:
    from tqdm import tqdm
except ImportError:
    def tqdm(iterable, desc="Processing"):
        print(f"{desc}...")
        return iterable

# Configuration
INPUT_DIR = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker_mp4"
OUTPUT_DIR = "/Users/<USER>/Desktop/LRP final/data/processed/vertical_adaptive_lips"
TARGET_SIZE = (96, 64)
TARGET_FRAMES = 24
TARGET_FPS = 15

class VerticalAdaptiveLipCropper:
    def __init__(self):
        self.processed_count = 0
        self.failed_count = 0
    
    def find_lip_vertical_position(self, frame):
        """
        Find the vertical position of lips in bottom-half face videos.
        Uses multiple detection methods to handle varying lip positions.
        """
        h, w, _ = frame.shape
        
        # Method 1: Color-based lip detection (most reliable for bottom-half faces)
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Red/pink lip detection
        lower_red1 = np.array([0, 40, 40])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 40, 40])
        upper_red2 = np.array([180, 255, 255])
        
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        lip_mask = cv2.bitwise_or(mask1, mask2)
        
        # Find horizontal lines with lip-colored pixels
        horizontal_sums = np.sum(lip_mask, axis=1)
        
        # Find the row with maximum lip pixels (likely lip center)
        if np.max(horizontal_sums) > w * 0.1:  # At least 10% of width should be lip-colored
            lip_center_y = np.argmax(horizontal_sums)
            
            # Find lip boundaries by looking for significant drops
            lip_top = lip_center_y
            lip_bottom = lip_center_y
            
            # Search upward for lip top
            for y in range(lip_center_y, max(0, lip_center_y - h//3), -1):
                if horizontal_sums[y] < horizontal_sums[lip_center_y] * 0.3:
                    lip_top = y
                    break
            
            # Search downward for lip bottom
            for y in range(lip_center_y, min(h, lip_center_y + h//3)):
                if horizontal_sums[y] < horizontal_sums[lip_center_y] * 0.3:
                    lip_bottom = y
                    break
            
            return lip_top, lip_bottom, lip_center_y
        
        # Method 2: Edge detection for mouth opening
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Apply Gaussian blur to reduce noise
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)
        
        # Detect horizontal edges (mouth opening appears as horizontal dark line)
        sobel_x = cv2.Sobel(blurred, cv2.CV_64F, 1, 0, ksize=3)
        sobel_y = cv2.Sobel(blurred, cv2.CV_64F, 0, 1, ksize=3)
        
        # Focus on horizontal features
        horizontal_edges = np.abs(sobel_y)
        
        # Sum horizontal edges across each row
        edge_sums = np.sum(horizontal_edges, axis=1)
        
        # Find peak in middle region (avoid top and bottom edges)
        search_start = h // 4
        search_end = 3 * h // 4
        
        if search_end > search_start:
            middle_region = edge_sums[search_start:search_end]
            if len(middle_region) > 0 and np.max(middle_region) > 0:
                relative_peak = np.argmax(middle_region)
                lip_center_y = search_start + relative_peak
                
                # Estimate lip region around detected center
                lip_height = h // 6  # Assume lips are about 1/6 of frame height
                lip_top = max(0, lip_center_y - lip_height // 2)
                lip_bottom = min(h, lip_center_y + lip_height // 2)
                
                return lip_top, lip_bottom, lip_center_y
        
        # Method 3: Fallback - assume lips are in middle third of frame
        lip_center_y = h // 2
        lip_height = h // 4
        lip_top = max(0, lip_center_y - lip_height // 2)
        lip_bottom = min(h, lip_center_y + lip_height // 2)
        
        return lip_top, lip_bottom, lip_center_y
    
    def extract_lip_region(self, frame):
        """Extract lip region based on detected vertical position."""
        h, w, _ = frame.shape
        
        # Find vertical position of lips
        lip_top, lip_bottom, lip_center_y = self.find_lip_vertical_position(frame)
        
        # Ensure minimum height
        lip_height = lip_bottom - lip_top
        if lip_height < h // 8:  # Minimum 1/8 of frame height
            lip_height = h // 6
            lip_top = max(0, lip_center_y - lip_height // 2)
            lip_bottom = min(h, lip_center_y + lip_height // 2)
        
        # Add vertical padding
        pad_y = int(lip_height * 0.3)
        y1 = max(0, lip_top - pad_y)
        y2 = min(h, lip_bottom + pad_y)
        
        # Horizontal cropping - center 70% of width (lips are usually centered)
        x_margin = int(w * 0.15)
        x1 = x_margin
        x2 = w - x_margin
        
        # Extract the region
        crop = frame[y1:y2, x1:x2]
        
        return crop
    
    def process_frame(self, frame):
        """Process a single frame to extract and normalize lip region."""
        # Extract lip region
        lip_crop = self.extract_lip_region(frame)
        
        if lip_crop is None or lip_crop.size == 0:
            return None
        
        # Convert to grayscale
        gray = cv2.cvtColor(lip_crop, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE for better contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)
        
        # Resize to target size
        resized = cv2.resize(enhanced, TARGET_SIZE)
        
        # Normalize pixel values
        normalized = cv2.equalizeHist(resized)
        
        return normalized
    
    def extract_frames(self, video_path):
        """Extract evenly distributed frames from video."""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames < TARGET_FRAMES:
            cap.release()
            return []
        
        # Get evenly spaced frame indices
        frame_indices = np.linspace(0, total_frames - 1, TARGET_FRAMES, dtype=int)
        frames = []
        
        for idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
            ret, frame = cap.read()
            if ret and frame is not None:
                frames.append(frame)
        
        cap.release()
        return frames
    
    def process_video(self, input_path, output_path):
        """Process complete video."""
        filename = os.path.basename(input_path)
        print(f"Processing: {filename}")
        
        # Extract frames
        frames = self.extract_frames(input_path)
        if len(frames) < TARGET_FRAMES:
            print(f"❌ Insufficient frames: {filename}")
            self.failed_count += 1
            return False
        
        # Process each frame
        processed_frames = []
        for frame in frames:
            processed = self.process_frame(frame)
            if processed is not None:
                processed_frames.append(processed)
        
        if len(processed_frames) < TARGET_FRAMES:
            print(f"❌ Processing failed: {filename}")
            self.failed_count += 1
            return False
        
        # Save output video
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        writer = cv2.VideoWriter(output_path, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)
        
        for frame in processed_frames:
            writer.write(frame)
        
        writer.release()
        
        # Verify output
        if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
            print(f"✅ Success: {filename}")
            self.processed_count += 1
            return True
        else:
            print(f"❌ Output failed: {filename}")
            self.failed_count += 1
            return False
    
    def process_all_videos(self):
        """Process all videos in input directory."""
        # Find all MP4 files
        video_files = glob(os.path.join(INPUT_DIR, "*.mp4"))
        
        if not video_files:
            print("❌ No MP4 files found!")
            return
        
        print(f"Found {len(video_files)} videos to process")
        print("🎯 Optimized for bottom-half faces with vertical lip position variation")
        
        # Process each video
        for video_path in tqdm(video_files, desc="Processing"):
            filename = os.path.basename(video_path)
            output_path = os.path.join(OUTPUT_DIR, filename)
            self.process_video(video_path, output_path)
        
        # Summary
        total = self.processed_count + self.failed_count
        success_rate = (self.processed_count / total * 100) if total > 0 else 0
        
        print(f"\n=== RESULTS ===")
        print(f"✅ Successful: {self.processed_count}")
        print(f"❌ Failed: {self.failed_count}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        print(f"📁 Output: {OUTPUT_DIR}")

def main():
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    cropper = VerticalAdaptiveLipCropper()
    cropper.process_all_videos()

if __name__ == "__main__":
    main()
