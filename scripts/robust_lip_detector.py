#!/usr/bin/env python3
"""
Robust Lip Detection and Cropping Script
Handles both GRID dataset videos (full faces) and user-generated videos (lips-only/mouth-region-only)
"""

import cv2
import numpy as np
import mediapipe as mp
import os
from typing import Optional, Tuple, List, Dict
import json

# Configuration
TARGET_SIZE = (96, 64)
TARGET_FRAMES = 24
TARGET_FPS = 15

# MediaPipe setup
mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
    static_image_mode=False,
    refine_landmarks=False,
    max_num_faces=1,
    min_detection_confidence=0.3,  # Lower threshold for difficult videos
    min_tracking_confidence=0.3
)

# Outer lip landmarks for MediaPipe
LIP_INDICES = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318]

class RobustLipDetector:
    def __init__(self, debug_mode=True):
        self.debug_mode = debug_mode
        self.detection_stats = {
            'mediapipe_full': 0,
            'mediapipe_regions': 0,
            'color_based': 0,
            'edge_based': 0,
            'motion_based': 0,
            'failed': 0
        }
    
    def detect_lips_mediapipe_full(self, frame) -> Optional[Tuple[int, int, int, int]]:
        """Standard MediaPipe detection on full frame."""
        h, w, _ = frame.shape
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = mp_face_mesh.process(rgb)
        
        if not results.multi_face_landmarks:
            return None
        
        return self._extract_lip_bbox_from_landmarks(results.multi_face_landmarks[0], w, h)
    
    def detect_lips_mediapipe_regions(self, frame) -> Optional[Tuple[int, int, int, int]]:
        """Try MediaPipe on different regions of the frame."""
        h, w, _ = frame.shape
        
        # Define regions to try (top_ratio, bottom_ratio)
        regions = [
            (0.3, 1.0),    # Bottom 70%
            (0.2, 0.8),    # Middle 60%
            (0.0, 0.7),    # Top 70%
            (0.4, 1.0),    # Bottom 60%
            (0.1, 0.9),    # Middle 80%
        ]
        
        for top_ratio, bottom_ratio in regions:
            y1 = int(h * top_ratio)
            y2 = int(h * bottom_ratio)
            region = frame[y1:y2, :]
            
            if region.shape[0] < 50:  # Skip too small regions
                continue
            
            rgb = cv2.cvtColor(region, cv2.COLOR_BGR2RGB)
            results = mp_face_mesh.process(rgb)
            
            if results.multi_face_landmarks:
                region_h, region_w = region.shape[:2]
                bbox = self._extract_lip_bbox_from_landmarks(
                    results.multi_face_landmarks[0], region_w, region_h
                )
                if bbox:
                    # Adjust coordinates back to full frame
                    x1, y1_rel, x2, y2_rel = bbox
                    return (x1, y1_rel + y1, x2, y2_rel + y1)
        
        return None
    
    def detect_lips_color_based(self, frame) -> Optional[Tuple[int, int, int, int]]:
        """Detect lips using color analysis - look for reddish regions."""
        h, w, _ = frame.shape
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Define red/pink color ranges for lips
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        # Create masks for red regions
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask = cv2.bitwise_or(mask1, mask2)
        
        # Focus on horizontally centered region
        center_x = w // 2
        roi_width = w // 3
        x_start = max(0, center_x - roi_width // 2)
        x_end = min(w, center_x + roi_width // 2)
        
        # Only consider the center region
        mask[:, :x_start] = 0
        mask[:, x_end:] = 0
        
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # Find the largest contour in the center region
        largest_contour = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(largest_contour)
        
        if area < 100:  # Minimum area threshold
            return None
        
        # Get bounding box
        x, y, w_box, h_box = cv2.boundingRect(largest_contour)
        
        # Add padding
        pad_x = int(w_box * 0.3)
        pad_y = int(h_box * 0.4)
        
        x1 = max(0, x - pad_x)
        y1 = max(0, y - pad_y)
        x2 = min(w, x + w_box + pad_x)
        y2 = min(h, y + h_box + pad_y)
        
        return (x1, y1, x2, y2)
    
    def detect_lips_edge_based(self, frame) -> Optional[Tuple[int, int, int, int]]:
        """Detect lips using edge detection and horizontal line analysis."""
        h, w, _ = frame.shape
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Focus on horizontally centered region
        center_x = w // 2
        roi_width = w // 2
        x_start = max(0, center_x - roi_width // 2)
        x_end = min(w, center_x + roi_width // 2)
        
        roi = gray[:, x_start:x_end]
        
        # Apply Gaussian blur and edge detection
        blurred = cv2.GaussianBlur(roi, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        
        # Look for horizontal edge patterns (lip line)
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 3))
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # Filter contours by aspect ratio (lips are wider than tall)
        valid_contours = []
        for contour in contours:
            x, y, w_box, h_box = cv2.boundingRect(contour)
            aspect_ratio = w_box / h_box if h_box > 0 else 0
            area = cv2.contourArea(contour)
            
            if 1.5 < aspect_ratio < 6.0 and area > 50:
                valid_contours.append(contour)
        
        if not valid_contours:
            return None
        
        # Get the largest valid contour
        largest_contour = max(valid_contours, key=cv2.contourArea)
        x, y, w_box, h_box = cv2.boundingRect(largest_contour)
        
        # Adjust coordinates back to full frame
        x += x_start
        
        # Add padding
        pad_x = int(w_box * 0.4)
        pad_y = int(h_box * 0.6)
        
        x1 = max(0, x - pad_x)
        y1 = max(0, y - pad_y)
        x2 = min(w, x + w_box + pad_x)
        y2 = min(h, y + h_box + pad_y)
        
        return (x1, y1, x2, y2)
    
    def detect_lips_motion_based(self, frames) -> Optional[Tuple[int, int, int, int]]:
        """Detect lips using motion analysis across multiple frames."""
        if len(frames) < 3:
            return None
        
        h, w, _ = frames[0].shape
        
        # Convert frames to grayscale
        gray_frames = [cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) for frame in frames[:3]]
        
        # Calculate frame differences
        diff1 = cv2.absdiff(gray_frames[0], gray_frames[1])
        diff2 = cv2.absdiff(gray_frames[1], gray_frames[2])
        
        # Combine differences
        motion = cv2.bitwise_or(diff1, diff2)
        
        # Threshold to get motion regions
        _, motion_thresh = cv2.threshold(motion, 25, 255, cv2.THRESH_BINARY)
        
        # Focus on center region
        center_x = w // 2
        roi_width = w // 2
        x_start = max(0, center_x - roi_width // 2)
        x_end = min(w, center_x + roi_width // 2)
        
        motion_roi = motion_thresh[:, x_start:x_end]
        
        # Find contours in motion
        contours, _ = cv2.findContours(motion_roi, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # Find contour with good aspect ratio for lips
        valid_contours = []
        for contour in contours:
            x, y, w_box, h_box = cv2.boundingRect(contour)
            aspect_ratio = w_box / h_box if h_box > 0 else 0
            area = cv2.contourArea(contour)
            
            if 1.2 < aspect_ratio < 5.0 and area > 30:
                valid_contours.append(contour)
        
        if not valid_contours:
            return None
        
        largest_contour = max(valid_contours, key=cv2.contourArea)
        x, y, w_box, h_box = cv2.boundingRect(largest_contour)
        
        # Adjust coordinates back to full frame
        x += x_start
        
        # Add padding
        pad_x = int(w_box * 0.5)
        pad_y = int(h_box * 0.7)
        
        x1 = max(0, x - pad_x)
        y1 = max(0, y - pad_y)
        x2 = min(w, x + w_box + pad_x)
        y2 = min(h, y + h_box + pad_y)
        
        return (x1, y1, x2, y2)
    
    def _extract_lip_bbox_from_landmarks(self, landmarks, w, h) -> Optional[Tuple[int, int, int, int]]:
        """Extract lip bounding box from MediaPipe landmarks."""
        lip_points = []
        
        for idx in LIP_INDICES:
            lm = landmarks.landmark[idx]
            x = int(lm.x * w)
            y = int(lm.y * h)
            if 0 <= x < w and 0 <= y < h:
                lip_points.append((x, y))
        
        if len(lip_points) < 10:
            return None
        
        xs = [p[0] for p in lip_points]
        ys = [p[1] for p in lip_points]
        
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        
        # Validate size
        lip_width = max_x - min_x
        lip_height = max_y - min_y
        
        if lip_width < 15 or lip_height < 8:
            return None
        
        # Add padding
        pad_x = int(lip_width * 0.4)
        pad_y = int(lip_height * 0.5)
        
        x1 = max(0, min_x - pad_x)
        y1 = max(0, min_y - pad_y)
        x2 = min(w, max_x + pad_x)
        y2 = min(h, max_y + pad_y)
        
        return (x1, y1, x2, y2)
    
    def detect_lips_robust(self, frame, frame_history=None) -> Tuple[Optional[Tuple[int, int, int, int]], str]:
        """
        Robust lip detection using multiple strategies with fallback logic.
        Returns (bbox, method_used)
        """
        
        # Strategy 1: Standard MediaPipe on full frame
        bbox = self.detect_lips_mediapipe_full(frame)
        if bbox:
            self.detection_stats['mediapipe_full'] += 1
            return bbox, 'mediapipe_full'
        
        # Strategy 2: MediaPipe on different regions
        bbox = self.detect_lips_mediapipe_regions(frame)
        if bbox:
            self.detection_stats['mediapipe_regions'] += 1
            return bbox, 'mediapipe_regions'
        
        # Strategy 3: Color-based detection
        bbox = self.detect_lips_color_based(frame)
        if bbox:
            self.detection_stats['color_based'] += 1
            return bbox, 'color_based'
        
        # Strategy 4: Edge-based detection
        bbox = self.detect_lips_edge_based(frame)
        if bbox:
            self.detection_stats['edge_based'] += 1
            return bbox, 'edge_based'
        
        # Strategy 5: Motion-based detection (if frame history available)
        if frame_history and len(frame_history) >= 3:
            bbox = self.detect_lips_motion_based(frame_history[-3:])
            if bbox:
                self.detection_stats['motion_based'] += 1
                return bbox, 'motion_based'
        
        # All strategies failed
        self.detection_stats['failed'] += 1
        return None, 'failed'

    def process_video(self, input_path, output_path, debug_output_path=None):
        """Process a single video with robust lip detection."""
        print(f"Processing: {os.path.basename(input_path)}")

        # Extract frames
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            print(f"❌ Cannot open video: {input_path}")
            return False, {}

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames < TARGET_FRAMES:
            print(f"❌ Insufficient frames: {total_frames}")
            cap.release()
            return False, {}

        # Sample frames evenly
        frame_indices = np.linspace(0, total_frames - 1, TARGET_FRAMES, dtype=int)
        frames = []

        for idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
            ret, frame = cap.read()
            if ret and frame is not None:
                frames.append(frame)

        cap.release()

        if len(frames) < TARGET_FRAMES:
            print(f"❌ Could not extract {TARGET_FRAMES} frames")
            return False, {}

        # Process each frame with robust detection
        valid_crops = []
        debug_frames = []
        detection_methods = []
        frame_history = []

        for i, frame in enumerate(frames):
            frame_history.append(frame)

            # Detect lips using robust method
            bbox, method = self.detect_lips_robust(frame, frame_history)

            if bbox is not None:
                # Extract and process crop
                x1, y1, x2, y2 = bbox
                crop = frame[y1:y2, x1:x2]

                if crop.size > 0:
                    # Convert to grayscale
                    gray_crop = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)

                    # Apply CLAHE for contrast enhancement
                    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
                    enhanced = clahe.apply(gray_crop)

                    # Resize to target size
                    resized = cv2.resize(enhanced, TARGET_SIZE)

                    # Normalize
                    normalized = cv2.equalizeHist(resized)

                    valid_crops.append(normalized)
                    detection_methods.append(method)

                    # Create debug frame if needed
                    if self.debug_mode:
                        debug_frame = frame.copy()
                        cv2.rectangle(debug_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(debug_frame, f"Frame {i+1}: {method}", (10, 30),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        debug_frames.append(debug_frame)

                    print(f"   ✅ Frame {i+1}/{len(frames)}: {method}")
                    continue

            print(f"   ❌ Frame {i+1}/{len(frames)}: No detection")

        # Check if we have enough valid detections
        detection_rate = (len(valid_crops) / len(frames)) * 100
        print(f"   📊 Detection rate: {detection_rate:.1f}% ({len(valid_crops)}/{len(frames)})")

        if len(valid_crops) < TARGET_FRAMES // 2:  # Need at least 50% success
            print(f"❌ Insufficient detections")
            return False, {}

        # Pad with duplicates if needed
        while len(valid_crops) < TARGET_FRAMES:
            if valid_crops:
                valid_crops.append(valid_crops[-1])
                detection_methods.append(detection_methods[-1] if detection_methods else 'duplicate')

        # Trim to exact target frames
        valid_crops = valid_crops[:TARGET_FRAMES]
        detection_methods = detection_methods[:TARGET_FRAMES]

        # Save main output
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)

        for crop in valid_crops:
            out.write(crop)

        out.release()

        # Save debug output if requested
        if self.debug_mode and debug_output_path and debug_frames:
            os.makedirs(os.path.dirname(debug_output_path), exist_ok=True)

            # Pad debug frames to match
            while len(debug_frames) < len(valid_crops):
                if debug_frames:
                    debug_frames.append(debug_frames[-1])

            debug_out = cv2.VideoWriter(debug_output_path, fourcc, TARGET_FPS,
                                      (frames[0].shape[1], frames[0].shape[0]))

            for debug_frame in debug_frames[:TARGET_FRAMES]:
                debug_out.write(debug_frame)

            debug_out.release()

        # Verify output
        success = os.path.exists(output_path) and os.path.getsize(output_path) > 1000

        if success:
            print(f"✅ Success: {os.path.basename(input_path)}")
        else:
            print(f"❌ Output failed: {os.path.basename(input_path)}")

        # Return results with detailed stats
        results = {
            'success': success,
            'detection_rate': detection_rate,
            'total_frames': len(frames),
            'valid_detections': len([m for m in detection_methods if m != 'duplicate']),
            'methods_used': list(set(detection_methods)),
            'method_counts': {method: detection_methods.count(method) for method in set(detection_methods)}
        }

        return success, results

def test_robust_detection():
    """Test the robust detection on both GRID and user videos."""

    # Test cases
    test_videos = [
        {
            'name': 'GRID Video (Full Face)',
            'path': '/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker_mp4/bbaf2n.mp4',
            'output': '/Users/<USER>/Desktop/LRP final/data/processed/robust_test_grid.mp4',
            'debug': '/Users/<USER>/Desktop/LRP final/data/debug/robust_test_grid_debug.mp4'
        },
        {
            'name': 'User Video (Lips Only)',
            'path': '/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker_mp4/doctor__useruser01__18to39__female__caucasian__20250804T033419 copy 2.mp4',
            'output': '/Users/<USER>/Desktop/LRP final/data/processed/robust_test_user.mp4',
            'debug': '/Users/<USER>/Desktop/LRP final/data/debug/robust_test_user_debug.mp4'
        }
    ]

    detector = RobustLipDetector(debug_mode=True)

    print("=== ROBUST LIP DETECTION TEST ===\n")

    all_results = {}

    for test_case in test_videos:
        print(f"Testing: {test_case['name']}")
        print(f"Input: {os.path.basename(test_case['path'])}")

        success, results = detector.process_video(
            test_case['path'],
            test_case['output'],
            test_case['debug']
        )

        all_results[test_case['name']] = results
        print()

    # Print summary
    print("=== DETECTION SUMMARY ===")
    print(f"Overall Detection Statistics:")
    for method, count in detector.detection_stats.items():
        print(f"  {method}: {count} detections")

    print(f"\nDetailed Results:")
    for name, results in all_results.items():
        if results:
            print(f"\n{name}:")
            print(f"  Success: {'✅' if results['success'] else '❌'}")
            print(f"  Detection Rate: {results['detection_rate']:.1f}%")
            print(f"  Methods Used: {', '.join(results['methods_used'])}")
            print(f"  Method Breakdown: {results['method_counts']}")

    # Save detailed results
    results_path = '/Users/<USER>/Desktop/LRP final/data/debug/robust_detection_results.json'
    os.makedirs(os.path.dirname(results_path), exist_ok=True)

    with open(results_path, 'w') as f:
        json.dump({
            'detection_stats': detector.detection_stats,
            'test_results': all_results
        }, f, indent=2)

    print(f"\n📊 Detailed results saved to: {results_path}")

if __name__ == "__main__":
    test_robust_detection()
