#!/usr/bin/env python3
"""
Robust Lip Detection and Cropping Script
Handles both GRID dataset videos (full faces) and user-generated videos (lips-only/mouth-region-only)
"""

import cv2
import numpy as np
import mediapipe as mp
import os
from typing import Optional, Tuple, List, Dict
import json

# Configuration
TARGET_SIZE = (96, 64)
TARGET_FRAMES = 24
TARGET_FPS = 15

# MediaPipe setup
mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
    static_image_mode=False,
    refine_landmarks=False,
    max_num_faces=1,
    min_detection_confidence=0.3,  # Lower threshold for difficult videos
    min_tracking_confidence=0.3
)

# Outer lip landmarks for MediaPipe
LIP_INDICES = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318]

class RobustLipDetector:
    def __init__(self, debug_mode=True):
        self.debug_mode = debug_mode
        self.detection_stats = {
            'mediapipe_full': 0,
            'mediapipe_regions': 0,
            'color_based': 0,
            'edge_based': 0,
            'motion_based': 0,
            'failed': 0
        }
    
    def detect_lips_mediapipe_full(self, frame) -> Optional[Tuple[int, int, int, int]]:
        """Standard MediaPipe detection on full frame."""
        h, w, _ = frame.shape
        rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = mp_face_mesh.process(rgb)
        
        if not results.multi_face_landmarks:
            return None
        
        return self._extract_lip_bbox_from_landmarks(results.multi_face_landmarks[0], w, h)
    
    def detect_lips_mediapipe_regions(self, frame) -> Optional[Tuple[int, int, int, int]]:
        """Try MediaPipe on different regions of the frame."""
        h, w, _ = frame.shape
        
        # Define regions to try (top_ratio, bottom_ratio)
        regions = [
            (0.3, 1.0),    # Bottom 70%
            (0.2, 0.8),    # Middle 60%
            (0.0, 0.7),    # Top 70%
            (0.4, 1.0),    # Bottom 60%
            (0.1, 0.9),    # Middle 80%
        ]
        
        for top_ratio, bottom_ratio in regions:
            y1 = int(h * top_ratio)
            y2 = int(h * bottom_ratio)
            region = frame[y1:y2, :]
            
            if region.shape[0] < 50:  # Skip too small regions
                continue
            
            rgb = cv2.cvtColor(region, cv2.COLOR_BGR2RGB)
            results = mp_face_mesh.process(rgb)
            
            if results.multi_face_landmarks:
                region_h, region_w = region.shape[:2]
                bbox = self._extract_lip_bbox_from_landmarks(
                    results.multi_face_landmarks[0], region_w, region_h
                )
                if bbox:
                    # Adjust coordinates back to full frame
                    x1, y1_rel, x2, y2_rel = bbox
                    return (x1, y1_rel + y1, x2, y2_rel + y1)
        
        return None
    
    def detect_lips_color_based(self, frame) -> Optional[Tuple[int, int, int, int]]:
        """Detect lips using color analysis - look for reddish regions."""
        h, w, _ = frame.shape
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Define red/pink color ranges for lips
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        # Create masks for red regions
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        mask = cv2.bitwise_or(mask1, mask2)
        
        # Focus on horizontally centered region
        center_x = w // 2
        roi_width = w // 3
        x_start = max(0, center_x - roi_width // 2)
        x_end = min(w, center_x + roi_width // 2)
        
        # Only consider the center region
        mask[:, :x_start] = 0
        mask[:, x_end:] = 0
        
        # Find contours
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # Find the largest contour in the center region
        largest_contour = max(contours, key=cv2.contourArea)
        area = cv2.contourArea(largest_contour)
        
        if area < 100:  # Minimum area threshold
            return None
        
        # Get bounding box
        x, y, w_box, h_box = cv2.boundingRect(largest_contour)
        
        # Add padding
        pad_x = int(w_box * 0.3)
        pad_y = int(h_box * 0.4)
        
        x1 = max(0, x - pad_x)
        y1 = max(0, y - pad_y)
        x2 = min(w, x + w_box + pad_x)
        y2 = min(h, y + h_box + pad_y)
        
        return (x1, y1, x2, y2)
    
    def detect_lips_edge_based(self, frame) -> Optional[Tuple[int, int, int, int]]:
        """Detect lips using edge detection and horizontal line analysis."""
        h, w, _ = frame.shape
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        
        # Focus on horizontally centered region
        center_x = w // 2
        roi_width = w // 2
        x_start = max(0, center_x - roi_width // 2)
        x_end = min(w, center_x + roi_width // 2)
        
        roi = gray[:, x_start:x_end]
        
        # Apply Gaussian blur and edge detection
        blurred = cv2.GaussianBlur(roi, (5, 5), 0)
        edges = cv2.Canny(blurred, 50, 150)
        
        # Look for horizontal edge patterns (lip line)
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (7, 3))
        edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # Filter contours by aspect ratio (lips are wider than tall)
        valid_contours = []
        for contour in contours:
            x, y, w_box, h_box = cv2.boundingRect(contour)
            aspect_ratio = w_box / h_box if h_box > 0 else 0
            area = cv2.contourArea(contour)
            
            if 1.5 < aspect_ratio < 6.0 and area > 50:
                valid_contours.append(contour)
        
        if not valid_contours:
            return None
        
        # Get the largest valid contour
        largest_contour = max(valid_contours, key=cv2.contourArea)
        x, y, w_box, h_box = cv2.boundingRect(largest_contour)
        
        # Adjust coordinates back to full frame
        x += x_start
        
        # Add padding
        pad_x = int(w_box * 0.4)
        pad_y = int(h_box * 0.6)
        
        x1 = max(0, x - pad_x)
        y1 = max(0, y - pad_y)
        x2 = min(w, x + w_box + pad_x)
        y2 = min(h, y + h_box + pad_y)
        
        return (x1, y1, x2, y2)
    
    def detect_lips_motion_based(self, frames) -> Optional[Tuple[int, int, int, int]]:
        """Detect lips using motion analysis across multiple frames."""
        if len(frames) < 3:
            return None
        
        h, w, _ = frames[0].shape
        
        # Convert frames to grayscale
        gray_frames = [cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) for frame in frames[:3]]
        
        # Calculate frame differences
        diff1 = cv2.absdiff(gray_frames[0], gray_frames[1])
        diff2 = cv2.absdiff(gray_frames[1], gray_frames[2])
        
        # Combine differences
        motion = cv2.bitwise_or(diff1, diff2)
        
        # Threshold to get motion regions
        _, motion_thresh = cv2.threshold(motion, 25, 255, cv2.THRESH_BINARY)
        
        # Focus on center region
        center_x = w // 2
        roi_width = w // 2
        x_start = max(0, center_x - roi_width // 2)
        x_end = min(w, center_x + roi_width // 2)
        
        motion_roi = motion_thresh[:, x_start:x_end]
        
        # Find contours in motion
        contours, _ = cv2.findContours(motion_roi, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if not contours:
            return None
        
        # Find contour with good aspect ratio for lips
        valid_contours = []
        for contour in contours:
            x, y, w_box, h_box = cv2.boundingRect(contour)
            aspect_ratio = w_box / h_box if h_box > 0 else 0
            area = cv2.contourArea(contour)
            
            if 1.2 < aspect_ratio < 5.0 and area > 30:
                valid_contours.append(contour)
        
        if not valid_contours:
            return None
        
        largest_contour = max(valid_contours, key=cv2.contourArea)
        x, y, w_box, h_box = cv2.boundingRect(largest_contour)
        
        # Adjust coordinates back to full frame
        x += x_start
        
        # Add padding
        pad_x = int(w_box * 0.5)
        pad_y = int(h_box * 0.7)
        
        x1 = max(0, x - pad_x)
        y1 = max(0, y - pad_y)
        x2 = min(w, x + w_box + pad_x)
        y2 = min(h, y + h_box + pad_y)
        
        return (x1, y1, x2, y2)

    def detect_lips_motion_based_enhanced(self, current_frame, frame_history) -> Optional[Tuple[int, int, int, int]]:
        """
        Enhanced motion-based lip detection with temporal consistency.
        Analyzes frame-to-frame differences to identify moving lip regions.
        """
        if len(frame_history) < 2:
            return None

        h, w, _ = current_frame.shape

        # Use current frame and previous frames for motion analysis
        frames_to_analyze = frame_history[-2:] + [current_frame]

        # Convert frames to grayscale for motion analysis
        gray_frames = [cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) for frame in frames_to_analyze]

        # Calculate multiple frame differences for better motion detection
        motion_maps = []
        for i in range(len(gray_frames) - 1):
            diff = cv2.absdiff(gray_frames[i], gray_frames[i + 1])
            motion_maps.append(diff)

        # Combine motion maps with weighted average (recent frames have more weight)
        if len(motion_maps) == 1:
            combined_motion = motion_maps[0]
        else:
            weights = [0.3, 0.7]  # More weight to recent motion
            combined_motion = np.zeros_like(motion_maps[0], dtype=np.float32)
            for i, motion_map in enumerate(motion_maps):
                combined_motion += motion_map.astype(np.float32) * weights[i]
            combined_motion = combined_motion.astype(np.uint8)

        # Apply Gaussian blur to reduce noise
        combined_motion = cv2.GaussianBlur(combined_motion, (5, 5), 0)

        # Adaptive threshold based on motion intensity
        motion_mean = np.mean(combined_motion)
        threshold_value = max(20, min(50, int(motion_mean * 1.5)))
        _, motion_thresh = cv2.threshold(combined_motion, threshold_value, 255, cv2.THRESH_BINARY)

        # Focus on center region where lips are typically located
        center_x = w // 2
        center_y = h // 2
        roi_width = int(w * 0.6)  # 60% of frame width
        roi_height = int(h * 0.5)  # 50% of frame height

        x_start = max(0, center_x - roi_width // 2)
        x_end = min(w, center_x + roi_width // 2)
        y_start = max(0, center_y - roi_height // 2)
        y_end = min(h, center_y + roi_height // 2)

        # Extract ROI
        motion_roi = motion_thresh[y_start:y_end, x_start:x_end]

        # Morphological operations to clean up motion regions
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
        motion_roi = cv2.morphologyEx(motion_roi, cv2.MORPH_CLOSE, kernel)
        motion_roi = cv2.morphologyEx(motion_roi, cv2.MORPH_OPEN, kernel)

        # Find contours in motion ROI
        contours, _ = cv2.findContours(motion_roi, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if not contours:
            return None

        # Filter contours by size and aspect ratio suitable for lips
        valid_contours = []
        min_area = (roi_width * roi_height) * 0.01  # At least 1% of ROI area
        max_area = (roi_width * roi_height) * 0.3   # At most 30% of ROI area

        for contour in contours:
            x, y, w_box, h_box = cv2.boundingRect(contour)
            aspect_ratio = w_box / h_box if h_box > 0 else 0
            area = cv2.contourArea(contour)

            # Lip-like aspect ratio (wider than tall) and reasonable size
            if (1.5 < aspect_ratio < 6.0 and
                min_area < area < max_area and
                w_box > 10 and h_box > 5):  # Minimum dimensions
                valid_contours.append(contour)

        if not valid_contours:
            return None

        # Select the contour closest to the center of the ROI
        roi_center_x = roi_width // 2
        roi_center_y = roi_height // 2

        best_contour = None
        min_distance = float('inf')

        for contour in valid_contours:
            x, y, w_box, h_box = cv2.boundingRect(contour)
            contour_center_x = x + w_box // 2
            contour_center_y = y + h_box // 2

            distance = np.sqrt((contour_center_x - roi_center_x)**2 +
                             (contour_center_y - roi_center_y)**2)

            if distance < min_distance:
                min_distance = distance
                best_contour = contour

        if best_contour is None:
            return None

        # Get bounding box of best contour
        x, y, w_box, h_box = cv2.boundingRect(best_contour)

        # Adjust coordinates back to full frame
        x += x_start
        y += y_start

        # Add padding for better lip coverage
        pad_x = int(w_box * 0.4)  # 40% padding horizontally
        pad_y = int(h_box * 0.6)  # 60% padding vertically

        x1 = max(0, x - pad_x)
        y1 = max(0, y - pad_y)
        x2 = min(w, x + w_box + pad_x)
        y2 = min(h, y + h_box + pad_y)

        return (x1, y1, x2, y2)

    def precise_crop_lip_region(self, frame, bbox) -> Optional[np.ndarray]:
        """
        Precisely crop and resize lip region to exact specifications.

        Args:
            frame: Input frame (BGR color)
            bbox: Detected bounding box (x1, y1, x2, y2)

        Returns:
            Precisely cropped and resized lip region (96x64 BGR) or None if invalid
        """
        if bbox is None:
            return None

        x1, y1, x2, y2 = bbox
        h, w = frame.shape[:2]

        # Validate bounding box
        if x1 >= x2 or y1 >= y2 or x1 < 0 or y1 < 0 or x2 > w or y2 > h:
            return None

        # Extract the crop
        crop = frame[y1:y2, x1:x2]

        if crop.size == 0:
            return None

        # Get current crop dimensions
        crop_h, crop_w = crop.shape[:2]

        # Calculate target aspect ratio (96/64 = 1.5)
        target_aspect = 96.0 / 64.0
        current_aspect = crop_w / crop_h

        # Adjust crop to match target aspect ratio while maintaining lip visibility
        if current_aspect > target_aspect:
            # Crop is too wide, need to trim width or add height
            new_width = int(crop_h * target_aspect)
            if new_width <= crop_w:
                # Trim width from center
                excess = crop_w - new_width
                left_trim = excess // 2
                crop = crop[:, left_trim:left_trim + new_width]
            else:
                # Add height (pad vertically)
                new_height = int(crop_w / target_aspect)
                excess = new_height - crop_h
                top_pad = excess // 2
                bottom_pad = excess - top_pad
                crop = cv2.copyMakeBorder(crop, top_pad, bottom_pad, 0, 0, cv2.BORDER_REPLICATE)
        else:
            # Crop is too tall, need to trim height or add width
            new_height = int(crop_w / target_aspect)
            if new_height <= crop_h:
                # Trim height from center
                excess = crop_h - new_height
                top_trim = excess // 2
                crop = crop[top_trim:top_trim + new_height, :]
            else:
                # Add width (pad horizontally)
                new_width = int(crop_h * target_aspect)
                excess = new_width - crop_w
                left_pad = excess // 2
                right_pad = excess - left_pad
                crop = cv2.copyMakeBorder(crop, 0, 0, left_pad, right_pad, cv2.BORDER_REPLICATE)

        # Apply CLAHE for contrast enhancement on each channel
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))

        # Split channels and apply CLAHE to each
        b, g, r = cv2.split(crop)
        b_enhanced = clahe.apply(b)
        g_enhanced = clahe.apply(g)
        r_enhanced = clahe.apply(r)
        enhanced = cv2.merge([b_enhanced, g_enhanced, r_enhanced])

        # Resize to exact target size (96x64)
        resized = cv2.resize(enhanced, TARGET_SIZE, interpolation=cv2.INTER_LANCZOS4)

        # Normalize to ensure proper color range
        normalized = cv2.normalize(resized, None, 0, 255, cv2.NORM_MINMAX).astype(np.uint8)

        return normalized

    def _extract_lip_bbox_from_landmarks(self, landmarks, w, h) -> Optional[Tuple[int, int, int, int]]:
        """Extract lip bounding box from MediaPipe landmarks."""
        lip_points = []
        
        for idx in LIP_INDICES:
            lm = landmarks.landmark[idx]
            x = int(lm.x * w)
            y = int(lm.y * h)
            if 0 <= x < w and 0 <= y < h:
                lip_points.append((x, y))
        
        if len(lip_points) < 10:
            return None
        
        xs = [p[0] for p in lip_points]
        ys = [p[1] for p in lip_points]
        
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        
        # Validate size
        lip_width = max_x - min_x
        lip_height = max_y - min_y
        
        if lip_width < 15 or lip_height < 8:
            return None
        
        # Add padding
        pad_x = int(lip_width * 0.4)
        pad_y = int(lip_height * 0.5)
        
        x1 = max(0, min_x - pad_x)
        y1 = max(0, min_y - pad_y)
        x2 = min(w, max_x + pad_x)
        y2 = min(h, max_y + pad_y)
        
        return (x1, y1, x2, y2)
    
    def detect_lips_robust(self, frame, frame_history=None) -> Tuple[Optional[Tuple[int, int, int, int]], str]:
        """
        Robust lip detection using multiple strategies with fallback logic.
        Motion-based detection is now primary since lips are the only moving element.
        Returns (bbox, method_used)
        """

        # Strategy 1: Enhanced motion-based detection (NEW PRIMARY METHOD)
        if frame_history and len(frame_history) >= 2:
            bbox = self.detect_lips_motion_based_enhanced(frame, frame_history)
            if bbox:
                self.detection_stats['motion_based'] += 1
                return bbox, 'motion_based'

        # Strategy 2: Standard MediaPipe on full frame
        bbox = self.detect_lips_mediapipe_full(frame)
        if bbox:
            self.detection_stats['mediapipe_full'] += 1
            return bbox, 'mediapipe_full'

        # Strategy 3: MediaPipe on different regions
        bbox = self.detect_lips_mediapipe_regions(frame)
        if bbox:
            self.detection_stats['mediapipe_regions'] += 1
            return bbox, 'mediapipe_regions'

        # Strategy 4: Color-based detection
        bbox = self.detect_lips_color_based(frame)
        if bbox:
            self.detection_stats['color_based'] += 1
            return bbox, 'color_based'

        # Strategy 5: Edge-based detection
        bbox = self.detect_lips_edge_based(frame)
        if bbox:
            self.detection_stats['edge_based'] += 1
            return bbox, 'edge_based'
        
        # All strategies failed
        self.detection_stats['failed'] += 1
        return None, 'failed'

    def process_video(self, input_path, output_path, debug_output_path=None):
        """Process a single video with robust lip detection."""
        print(f"Processing: {os.path.basename(input_path)}")

        # Extract frames
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            print(f"❌ Cannot open video: {input_path}")
            return False, {}

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames < TARGET_FRAMES:
            print(f"❌ Insufficient frames: {total_frames}")
            cap.release()
            return False, {}

        # Sample frames evenly
        frame_indices = np.linspace(0, total_frames - 1, TARGET_FRAMES, dtype=int)
        frames = []

        for idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
            ret, frame = cap.read()
            if ret and frame is not None:
                frames.append(frame)

        cap.release()

        if len(frames) < TARGET_FRAMES:
            print(f"❌ Could not extract {TARGET_FRAMES} frames")
            return False, {}

        # Process each frame with robust detection
        valid_crops = []
        debug_frames = []
        detection_methods = []
        frame_history = []

        for i, frame in enumerate(frames):
            frame_history.append(frame)

            # Detect lips using robust method
            bbox, method = self.detect_lips_robust(frame, frame_history)

            if bbox is not None:
                # Apply precise cropping to detected lip region
                precisely_cropped = self.precise_crop_lip_region(frame, bbox)

                if precisely_cropped is not None:
                    valid_crops.append(precisely_cropped)
                    detection_methods.append(method)

                    # Create debug frame if needed
                    if self.debug_mode:
                        x1, y1, x2, y2 = bbox
                        debug_frame = frame.copy()
                        cv2.rectangle(debug_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(debug_frame, f"Frame {i+1}: {method}", (10, 30),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        cv2.putText(debug_frame, f"Cropped: 96x64", (10, 60),
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
                        debug_frames.append(debug_frame)

                    print(f"   ✅ Frame {i+1}/{len(frames)}: {method}")
                    continue

            print(f"   ❌ Frame {i+1}/{len(frames)}: No detection")

        # Check if we have enough valid detections
        detection_rate = (len(valid_crops) / len(frames)) * 100
        print(f"   📊 Detection rate: {detection_rate:.1f}% ({len(valid_crops)}/{len(frames)})")

        if len(valid_crops) < TARGET_FRAMES // 2:  # Need at least 50% success
            print(f"❌ Insufficient detections")
            return False, {}

        # Pad with duplicates if needed
        while len(valid_crops) < TARGET_FRAMES:
            if valid_crops:
                valid_crops.append(valid_crops[-1])
                detection_methods.append(detection_methods[-1] if detection_methods else 'duplicate')

        # Trim to exact target frames
        valid_crops = valid_crops[:TARGET_FRAMES]
        detection_methods = detection_methods[:TARGET_FRAMES]

        # Save main output
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, TARGET_FPS, TARGET_SIZE, isColor=True)

        for crop in valid_crops:
            out.write(crop)

        out.release()

        # Save debug output if requested
        if self.debug_mode and debug_output_path and debug_frames:
            os.makedirs(os.path.dirname(debug_output_path), exist_ok=True)

            # Pad debug frames to match
            while len(debug_frames) < len(valid_crops):
                if debug_frames:
                    debug_frames.append(debug_frames[-1])

            debug_out = cv2.VideoWriter(debug_output_path, fourcc, TARGET_FPS,
                                      (frames[0].shape[1], frames[0].shape[0]))

            for debug_frame in debug_frames[:TARGET_FRAMES]:
                debug_out.write(debug_frame)

            debug_out.release()

        # Verify output
        success = os.path.exists(output_path) and os.path.getsize(output_path) > 1000

        if success:
            print(f"✅ Success: {os.path.basename(input_path)}")
        else:
            print(f"❌ Output failed: {os.path.basename(input_path)}")

        # Return results with detailed stats
        results = {
            'success': success,
            'detection_rate': detection_rate,
            'total_frames': len(frames),
            'valid_detections': len([m for m in detection_methods if m != 'duplicate']),
            'methods_used': list(set(detection_methods)),
            'method_counts': {method: detection_methods.count(method) for method in set(detection_methods)}
        }

        return success, results

def test_comprehensive_dataset():
    """Test the robust detection on the complete dataset (all 56 videos)."""

    input_dir = '/Users/<USER>/Desktop/LRP final/data/raw/new_data_to_try'
    output_dir = '/Users/<USER>/Desktop/LRP final/data/processed/new_motion_cropped_lips'
    debug_dir = '/Users/<USER>/Desktop/LRP final/data/debug/new_motion_cropped_debug'

    # Find all video files (MP4 and MPG)
    import glob
    video_files = []
    video_files.extend(glob.glob(os.path.join(input_dir, "*.mp4")))
    video_files.extend(glob.glob(os.path.join(input_dir, "*.mpg")))

    if not video_files:
        print("❌ No video files found!")
        return

    print(f"=== MOTION-ENHANCED LIP DETECTION WITH PRECISE CROPPING ===")
    print(f"📁 Input Directory: {input_dir}")
    print(f"📁 Output Directory: {output_dir}")
    print(f"🔍 Debug Directory: {debug_dir}")
    print(f"🎬 Total Videos: {len(video_files)}")
    print(f"🎨 Output Format: Color (BGR)")
    print(f"📐 Resolution: 96×64 (precisely cropped)")
    print(f"🎞️ Frames: 24 @ 15fps")
    print(f"🚀 Primary Method: Enhanced Motion Detection")
    print(f"✂️ Processing: Precise lip region cropping\n")

    detector = RobustLipDetector(debug_mode=True)

    # Create output directories
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(debug_dir, exist_ok=True)

    all_results = {}
    successful_videos = []
    failed_videos = []

    # Process each video
    for i, video_path in enumerate(video_files, 1):
        filename = os.path.basename(video_path)
        print(f"[{i:2d}/{len(video_files)}] Processing: {filename}")

        output_path = os.path.join(output_dir, filename)
        debug_path = os.path.join(debug_dir, f"debug_{filename}")

        success, results = detector.process_video(video_path, output_path, debug_path)

        all_results[filename] = results

        if success:
            successful_videos.append(filename)
        else:
            failed_videos.append(filename)

        print()

    # Calculate overall statistics
    total_videos = len(video_files)
    successful_count = len(successful_videos)
    failed_count = len(failed_videos)
    success_rate = (successful_count / total_videos * 100) if total_videos > 0 else 0

    print("=" * 60)
    print("🏆 COMPREHENSIVE TEST RESULTS")
    print("=" * 60)

    print(f"\n📊 OVERALL STATISTICS:")
    print(f"   Total Videos Processed: {total_videos}")
    print(f"   ✅ Successful: {successful_count}")
    print(f"   ❌ Failed: {failed_count}")
    print(f"   📈 Success Rate: {success_rate:.1f}%")

    # Compare with previous results
    previous_success_rate = 39.3  # From fixed MediaPipe script (22/56)
    previous_successful = 22
    improvement = success_rate - previous_success_rate
    additional_videos = successful_count - previous_successful

    print(f"\n📈 IMPROVEMENT ANALYSIS:")
    print(f"   Previous Success Rate: {previous_success_rate:.1f}% ({previous_successful}/56)")
    print(f"   Current Success Rate: {success_rate:.1f}% ({successful_count}/56)")
    print(f"   Improvement: {improvement:+.1f} percentage points")
    print(f"   Additional Videos Processed: {additional_videos:+d}")

    print(f"\n🔧 DETECTION METHOD STATISTICS:")
    total_detections = sum(detector.detection_stats.values())
    for method, count in detector.detection_stats.items():
        percentage = (count / total_detections * 100) if total_detections > 0 else 0
        print(f"   {method}: {count:4d} detections ({percentage:5.1f}%)")

    # Method distribution analysis
    method_usage = {}
    for filename, results in all_results.items():
        if results and results.get('success'):
            for method in results.get('methods_used', []):
                method_usage[method] = method_usage.get(method, 0) + 1

    print(f"\n📋 VIDEO-LEVEL METHOD USAGE:")
    for method, count in sorted(method_usage.items(), key=lambda x: x[1], reverse=True):
        percentage = (count / successful_count * 100) if successful_count > 0 else 0
        print(f"   {method}: {count:2d} videos ({percentage:5.1f}%)")

    # List failed videos for analysis
    if failed_videos:
        print(f"\n❌ FAILED VIDEOS ({len(failed_videos)}):")
        for filename in sorted(failed_videos):
            print(f"   • {filename}")

    # List successful videos by method
    print(f"\n✅ SUCCESSFUL VIDEOS BY PRIMARY METHOD:")
    method_videos = {}
    for filename, results in all_results.items():
        if results and results.get('success'):
            primary_method = results.get('methods_used', ['unknown'])[0]
            if primary_method not in method_videos:
                method_videos[primary_method] = []
            method_videos[primary_method].append(filename)

    for method, videos in sorted(method_videos.items()):
        print(f"\n   {method.upper()} ({len(videos)} videos):")
        for filename in sorted(videos)[:5]:  # Show first 5
            print(f"     • {filename}")
        if len(videos) > 5:
            print(f"     ... and {len(videos) - 5} more")

    # Save comprehensive results
    results_path = '/Users/<USER>/Desktop/LRP final/data/debug/new_motion_cropped_results.json'

    comprehensive_data = {
        'summary': {
            'total_videos': total_videos,
            'successful_count': successful_count,
            'failed_count': failed_count,
            'success_rate': success_rate,
            'previous_success_rate': previous_success_rate,
            'improvement': improvement,
            'additional_videos': additional_videos
        },
        'detection_stats': detector.detection_stats,
        'method_usage': method_usage,
        'successful_videos': successful_videos,
        'failed_videos': failed_videos,
        'detailed_results': all_results
    }

    with open(results_path, 'w') as f:
        json.dump(comprehensive_data, f, indent=2)

    print(f"\n📊 Comprehensive results saved to: {results_path}")
    print(f"📁 Color lip videos saved to: {output_dir}")
    print(f"🔍 Debug videos saved to: {debug_dir}")

def test_robust_detection():
    """Test the robust detection on both GRID and user videos."""

    # Test cases
    test_videos = [
        {
            'name': 'GRID Video (Full Face)',
            'path': '/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker_mp4/bbaf2n.mp4',
            'output': '/Users/<USER>/Desktop/LRP final/data/processed/robust_test_grid_color.mp4',
            'debug': '/Users/<USER>/Desktop/LRP final/data/debug/robust_test_grid_color_debug.mp4'
        },
        {
            'name': 'User Video (Lips Only)',
            'path': '/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker_mp4/doctor__useruser01__18to39__female__caucasian__20250804T033419 copy 2.mp4',
            'output': '/Users/<USER>/Desktop/LRP final/data/processed/robust_test_user_color.mp4',
            'debug': '/Users/<USER>/Desktop/LRP final/data/debug/robust_test_user_color_debug.mp4'
        }
    ]

    detector = RobustLipDetector(debug_mode=True)

    print("=== ROBUST LIP DETECTION TEST (COLOR OUTPUT) ===\n")

    all_results = {}

    for test_case in test_videos:
        print(f"Testing: {test_case['name']}")
        print(f"Input: {os.path.basename(test_case['path'])}")

        success, results = detector.process_video(
            test_case['path'],
            test_case['output'],
            test_case['debug']
        )

        all_results[test_case['name']] = results
        print()

    # Print summary
    print("=== DETECTION SUMMARY ===")
    print(f"Overall Detection Statistics:")
    for method, count in detector.detection_stats.items():
        print(f"  {method}: {count} detections")

    print(f"\nDetailed Results:")
    for name, results in all_results.items():
        if results:
            print(f"\n{name}:")
            print(f"  Success: {'✅' if results['success'] else '❌'}")
            print(f"  Detection Rate: {results['detection_rate']:.1f}%")
            print(f"  Methods Used: {', '.join(results['methods_used'])}")
            print(f"  Method Breakdown: {results['method_counts']}")

    # Save detailed results
    results_path = '/Users/<USER>/Desktop/LRP final/data/debug/robust_detection_color_results.json'
    os.makedirs(os.path.dirname(results_path), exist_ok=True)

    with open(results_path, 'w') as f:
        json.dump({
            'detection_stats': detector.detection_stats,
            'test_results': all_results
        }, f, indent=2)

    print(f"\n📊 Detailed results saved to: {results_path}")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--comprehensive":
        test_comprehensive_dataset()
    else:
        test_robust_detection()
