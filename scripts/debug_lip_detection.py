#!/usr/bin/env python3
"""
Visual Debug Mode for Lip Detection
-----------------------------------
Shows bounding boxes around detected lips to verify alignment.
"""

import os
import cv2
import numpy as np
import mediapipe as mp
from glob import glob

# ---- MediaPipe ----
mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
    static_image_mode=False,
    refine_landmarks=False,
    max_num_faces=1,
    min_detection_confidence=0.4,
    min_tracking_confidence=0.4
)

# Lip landmark indices (outer + inner)
LIP_INDICES = [
    61, 78, 191, 80, 81, 82, 13, 312, 308, 324, 318, 402, 317, 14,
    87, 178, 88, 95, 78, 61
]

def detect_lips_debug(frame):
    """Detect lips and return bounding box coordinates."""
    h, w, _ = frame.shape
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = mp_face_mesh.process(rgb)
    
    if not results.multi_face_landmarks:
        return None, None

    pts = []
    for idx in LIP_INDICES:
        lm = results.multi_face_landmarks[0].landmark[idx]
        pts.append((int(lm.x * w), int(lm.y * h)))
    
    if not pts:
        return None, None

    xs = [p[0] for p in pts]
    ys = [p[1] for p in pts]
    x1, x2 = max(0, min(xs)), min(w, max(xs))
    y1, y2 = max(0, min(ys)), min(h, max(ys))

    # Add padding
    pad_x = int((x2 - x1) * 0.3)
    pad_y = int((y2 - y1) * 0.4)
    x1_pad, x2_pad = max(0, x1 - pad_x), min(w, x2 + pad_x)
    y1_pad, y2_pad = max(0, y1 - pad_y), min(h, y2 + pad_y)

    return (x1, y1, x2, y2), (x1_pad, y1_pad, x2_pad, y2_pad)

def create_debug_video(input_path, output_path):
    """Create debug video showing lip detection boxes."""
    cap = cv2.VideoCapture(input_path)
    if not cap.isOpened():
        print(f"❌ Cannot open {input_path}")
        return False

    # Get video properties
    fps = cap.get(cv2.CAP_PROP_FPS)
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    
    # Create output video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (width, height))
    
    frame_count = 0
    detection_count = 0
    
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        
        frame_count += 1
        
        # Detect lips
        lip_box, padded_box = detect_lips_debug(frame)
        
        if lip_box is not None:
            detection_count += 1
            x1, y1, x2, y2 = lip_box
            x1_pad, y1_pad, x2_pad, y2_pad = padded_box
            
            # Draw original lip detection (red)
            cv2.rectangle(frame, (x1, y1), (x2, y2), (0, 0, 255), 2)
            cv2.putText(frame, "LIPS", (x1, y1-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 0, 255), 1)
            
            # Draw padded region (green)
            cv2.rectangle(frame, (x1_pad, y1_pad), (x2_pad, y2_pad), (0, 255, 0), 2)
            cv2.putText(frame, "CROP", (x1_pad, y1_pad-10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)
        else:
            # No detection - draw red X
            cv2.line(frame, (width//2-20, height//2-20), (width//2+20, height//2+20), (0, 0, 255), 3)
            cv2.line(frame, (width//2+20, height//2-20), (width//2-20, height//2+20), (0, 0, 255), 3)
            cv2.putText(frame, "NO DETECTION", (width//2-60, height//2+40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        
        # Add frame info
        cv2.putText(frame, f"Frame: {frame_count}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        cv2.putText(frame, f"Detections: {detection_count}/{frame_count}", (10, 60), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
        
        out.write(frame)
    
    cap.release()
    out.release()
    
    detection_rate = (detection_count / frame_count * 100) if frame_count > 0 else 0
    print(f"✅ Debug video created: {os.path.basename(output_path)}")
    print(f"   📊 Detection rate: {detection_rate:.1f}% ({detection_count}/{frame_count})")
    
    return True

def main():
    input_dir = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker_mp4"
    output_dir = "/Users/<USER>/Desktop/LRP final/data/debug/lip_detection"
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Get a few sample videos for debugging
    videos = glob(os.path.join(input_dir, "*.mp4"))[:5]  # Just first 5 for debugging
    
    if not videos:
        print("❌ No MP4 files found!")
        return
    
    print(f"Creating debug videos for {len(videos)} samples...")
    
    for video_path in videos:
        filename = os.path.basename(video_path)
        debug_filename = f"debug_{filename}"
        output_path = os.path.join(output_dir, debug_filename)
        
        print(f"\n🔍 Processing: {filename}")
        create_debug_video(video_path, output_path)
    
    print(f"\n✅ Debug videos saved to: {output_dir}")
    print("🔍 Review these videos to verify lip detection accuracy before full processing")

if __name__ == "__main__":
    main()
