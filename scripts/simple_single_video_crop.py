#!/usr/bin/env python3
"""
Simple single video lip cropping
"""

import cv2
import numpy as np
import mediapipe as mp

# Configuration
INPUT_VIDEO = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker_mp4/bbaf2n.mp4"
OUTPUT_VIDEO = "/Users/<USER>/Desktop/LRP final/data/processed/single_test_bbaf2n.mp4"
TARGET_SIZE = (96, 64)
TARGET_FRAMES = 32
TARGET_FPS = 15

# MediaPipe setup
mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
    static_image_mode=False,
    refine_landmarks=False,
    max_num_faces=1,
    min_detection_confidence=0.5,
    min_tracking_confidence=0.5
)

# Outer lip landmarks
LIP_INDICES = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318]

def detect_lips(frame):
    """Detect lips and return bounding box."""
    h, w, _ = frame.shape
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = mp_face_mesh.process(rgb)
    
    if not results.multi_face_landmarks:
        return None
    
    landmarks = results.multi_face_landmarks[0]
    lip_points = []
    
    for idx in LIP_INDICES:
        lm = landmarks.landmark[idx]
        x = int(lm.x * w)
        y = int(lm.y * h)
        if 0 <= x < w and 0 <= y < h:
            lip_points.append((x, y))
    
    if len(lip_points) < 10:
        return None
    
    xs = [p[0] for p in lip_points]
    ys = [p[1] for p in lip_points]
    
    min_x, max_x = min(xs), max(xs)
    min_y, max_y = min(ys), max(ys)
    
    # Add padding
    lip_width = max_x - min_x
    lip_height = max_y - min_y
    
    pad_x = int(lip_width * 0.4)
    pad_y = int(lip_height * 0.5)
    
    x1 = max(0, min_x - pad_x)
    y1 = max(0, min_y - pad_y)
    x2 = min(w, max_x + pad_x)
    y2 = min(h, max_y + pad_y)
    
    return (x1, y1, x2, y2)

def main():
    # Open video
    cap = cv2.VideoCapture(INPUT_VIDEO)
    if not cap.isOpened():
        print("❌ Cannot open video")
        return
    
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    print(f"Total frames: {total_frames}")
    
    # Sample frames evenly
    frame_indices = np.linspace(0, total_frames - 1, TARGET_FRAMES, dtype=int)
    processed_frames = []
    
    for i, frame_idx in enumerate(frame_indices):
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        
        if not ret:
            print(f"❌ Cannot read frame {frame_idx}")
            continue
        
        # Detect lips
        bbox = detect_lips(frame)
        
        if bbox is None:
            print(f"⚠️ No lips detected in frame {i+1}")
            continue
        
        # Crop lips
        x1, y1, x2, y2 = bbox
        crop = frame[y1:y2, x1:x2]
        
        if crop.size == 0:
            print(f"⚠️ Empty crop in frame {i+1}")
            continue
        
        # Convert to grayscale
        gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
        
        # Resize to target
        resized = cv2.resize(gray, TARGET_SIZE)
        
        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(resized)
        
        processed_frames.append(enhanced)
        print(f"✅ Processed frame {i+1}/{TARGET_FRAMES}")
    
    cap.release()
    
    if len(processed_frames) < TARGET_FRAMES:
        print(f"⚠️ Only got {len(processed_frames)} frames, padding...")
        while len(processed_frames) < TARGET_FRAMES:
            if processed_frames:
                processed_frames.append(processed_frames[-1])
    
    # Save output
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(OUTPUT_VIDEO, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)
    
    for frame in processed_frames[:TARGET_FRAMES]:
        out.write(frame)
    
    out.release()
    
    print(f"✅ Output saved: {OUTPUT_VIDEO}")
    print(f"📊 Final frames: {len(processed_frames[:TARGET_FRAMES])}")

if __name__ == "__main__":
    main()
