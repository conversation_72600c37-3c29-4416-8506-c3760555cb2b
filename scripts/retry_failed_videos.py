#!/usr/bin/env python3
"""
Automated retry script for failed video preprocessing.
Handles re-encoding, jitter augmentation, and reprocessing of failed videos.
"""

import os
import sys
import glob
import shutil
import subprocess
import cv2
import numpy as np
from pathlib import Path
import argparse
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoRetryProcessor:
    def __init__(self, input_dir, output_dir, retry_base_dir):
        self.input_dir = Path(input_dir)
        self.output_dir = Path(output_dir)
        self.retry_base_dir = Path(retry_base_dir)
        
        # Create retry subdirectories
        self.retry_dir = self.retry_base_dir / "failed_originals"
        self.reencoded_dir = self.retry_base_dir / "reencoded"
        self.jittered_dir = self.retry_base_dir / "jittered"
        self.final_output_dir = self.retry_base_dir / "processed"
        
        for dir_path in [self.retry_dir, self.reencoded_dir, self.jittered_dir, self.final_output_dir]:
            dir_path.mkdir(parents=True, exist_ok=True)

    def phase_a_find_failed(self):
        """Phase A: Find videos that failed processing"""
        logger.info("Phase A: Finding failed videos...")
        
        video_extensions = ("*.mp4", "*.MP4", "*.mov", "*.MOV", "*.mpg", "*.mpeg", "*.MPEG")
        failed_videos = []
        
        # Find all input videos
        input_videos = []
        for ext in video_extensions:
            input_videos.extend(self.input_dir.rglob(ext))
        
        logger.info(f"Found {len(input_videos)} total input videos")
        
        # Check which ones are missing or too small in output
        for input_video in input_videos:
            # Expected output path
            rel_path = input_video.relative_to(self.input_dir)
            expected_output = self.output_dir / (rel_path.stem + ".mp4")
            
            # Check if output exists and is reasonable size
            if not expected_output.exists() or expected_output.stat().st_size < 2000:
                failed_videos.append(input_video)
                # Copy to retry folder
                shutil.copy2(input_video, self.retry_dir / input_video.name)
        
        logger.info(f"Found {len(failed_videos)} failed videos")
        for video in failed_videos:
            logger.info(f"  Failed: {video.name}")
        
        return failed_videos

    def phase_b_reencode(self):
        """Phase B: Re-encode videos to fix container/codec issues"""
        logger.info("Phase B: Re-encoding failed videos...")
        
        failed_videos = list(self.retry_dir.glob("*"))
        video_files = [f for f in failed_videos if f.suffix.lower() in ['.mp4', '.mov', '.mpg', '.mpeg']]
        
        logger.info(f"Re-encoding {len(video_files)} videos...")
        
        for video_file in video_files:
            output_file = self.reencoded_dir / (video_file.stem + ".mp4")
            
            # FFmpeg command for re-encoding
            cmd = [
                'ffmpeg', '-y', '-hide_banner', '-loglevel', 'error',
                '-i', str(video_file),
                '-vf', 'fps=15,scale=640:480,format=yuv420p',
                '-c:v', 'libx264', '-preset', 'fast', '-crf', '18',
                '-an',  # No audio
                str(output_file)
            ]
            
            try:
                subprocess.run(cmd, check=True, capture_output=True)
                logger.info(f"  Re-encoded: {video_file.name}")
            except subprocess.CalledProcessError as e:
                logger.error(f"  Failed to re-encode {video_file.name}: {e}")
        
        return list(self.reencoded_dir.glob("*.mp4"))

    def phase_c_jitter_augment(self):
        """Phase C: Apply micro-jitter to create frame variation for static videos"""
        logger.info("Phase C: Applying jitter augmentation...")
        
        reencoded_videos = list(self.reencoded_dir.glob("*.mp4"))
        
        for video_file in reencoded_videos:
            self._apply_jitter_to_video(video_file)
        
        return list(self.jittered_dir.glob("*.mp4"))

    def _apply_jitter_to_video(self, video_path):
        """Apply subtle jitter augmentation to a single video"""
        cap = cv2.VideoCapture(str(video_path))
        if not cap.isOpened():
            logger.error(f"Cannot open video: {video_path}")
            return
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS) or 15.0
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH) or 640)
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT) or 480)
        
        # Output video
        output_path = self.jittered_dir / video_path.name
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, 15.0, (width, height))
        
        # Random number generator with fixed seed for reproducibility
        rng = np.random.default_rng(42)
        
        frame_count = 0
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Apply subtle jitter
            # Translation: ±2 pixels
            tx = int(rng.integers(-2, 3))
            ty = int(rng.integers(-2, 3))
            
            # Scale: 0.997 to 1.003 (very subtle)
            scale = float(rng.uniform(0.997, 1.003))
            
            # Create affine transformation matrix
            M = np.array([[scale, 0, tx], [0, scale, ty]], dtype=np.float32)
            
            # Apply transformation
            jittered_frame = cv2.warpAffine(frame, M, (width, height), borderMode=cv2.BORDER_REFLECT)
            
            # Apply subtle brightness variation
            brightness_mult = float(rng.uniform(0.98, 1.02))
            jittered_frame = cv2.convertScaleAbs(jittered_frame, alpha=brightness_mult, beta=0)
            
            out.write(jittered_frame)
            frame_count += 1
        
        cap.release()
        out.release()
        
        logger.info(f"  Jittered: {video_path.name} ({frame_count} frames)")

    def phase_d_reprocess(self, preprocess_script_path):
        """Phase D: Re-run preprocessing on jittered videos"""
        logger.info("Phase D: Re-running preprocessing on jittered videos...")
        
        # Run preprocessing script on jittered videos
        cmd = [
            sys.executable, str(preprocess_script_path),
            '--input', str(self.jittered_dir),
            '--output', str(self.final_output_dir),
            '--verbose'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            logger.info("Preprocessing completed")
            logger.info(f"STDOUT: {result.stdout}")
            if result.stderr:
                logger.warning(f"STDERR: {result.stderr}")
            
            # Copy successful outputs back to main output directory
            processed_videos = list(self.final_output_dir.glob("*.mp4"))
            copied_count = 0
            
            for video in processed_videos:
                dest = self.output_dir / video.name
                if not dest.exists():
                    shutil.copy2(video, dest)
                    copied_count += 1
                    logger.info(f"  Copied: {video.name}")
            
            logger.info(f"Copied {copied_count} newly processed videos to main output directory")
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Preprocessing failed: {e}")

    def run_full_retry(self, preprocess_script_path):
        """Run all phases of the retry process"""
        logger.info("Starting full retry process...")
        
        # Phase A: Find failed videos
        failed_videos = self.phase_a_find_failed()
        if not failed_videos:
            logger.info("No failed videos found!")
            return
        
        # Phase B: Re-encode
        reencoded_videos = self.phase_b_reencode()
        
        # Phase C: Apply jitter
        jittered_videos = self.phase_c_jitter_augment()
        
        # Phase D: Reprocess
        self.phase_d_reprocess(preprocess_script_path)
        
        logger.info("Retry process completed!")

def main():
    parser = argparse.ArgumentParser(description="Retry failed video preprocessing")
    parser.add_argument('--input', required=True, help='Original input directory')
    parser.add_argument('--output', required=True, help='Main output directory')
    parser.add_argument('--retry-dir', required=True, help='Base directory for retry operations')
    parser.add_argument('--preprocess-script', required=True, help='Path to preprocessing script')
    parser.add_argument('--phase', choices=['a', 'b', 'c', 'd', 'all'], default='all',
                       help='Which phase to run (default: all)')
    
    args = parser.parse_args()
    
    processor = VideoRetryProcessor(args.input, args.output, args.retry_dir)
    
    if args.phase == 'all':
        processor.run_full_retry(args.preprocess_script)
    elif args.phase == 'a':
        processor.phase_a_find_failed()
    elif args.phase == 'b':
        processor.phase_b_reencode()
    elif args.phase == 'c':
        processor.phase_c_jitter_augment()
    elif args.phase == 'd':
        processor.phase_d_reprocess(args.preprocess_script)

if __name__ == "__main__":
    main()
