#!/usr/bin/env python3
"""
ICU Lipreading Preprocessing – Adaptive Lip ROI (MediaPipe)
----------------------------------------------------------
Real videos only. Detects lips once per video using FaceMesh,
adapts to vertical offset and lip size, smooths ROI, and
outputs normalised 96×64 grayscale clips (24f @ 15fps).
"""

import os, cv2, numpy as np
from glob import glob

# Try to import tqdm, fallback if not available
try:
    from tqdm import tqdm
except ImportError:
    def tqdm(iterable, desc="Processing"):
        print(f"{desc}...")
        return iterable

# ==== CONFIG ====
INPUT_DIR  = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker_mp4"
OUTPUT_DIR = "/Users/<USER>/Desktop/LRP final/data/processed/real_adaptive"
TARGET_SIZE   = (96, 64)     # width, height
TARGET_FRAMES = 24
TARGET_FPS    = 15
MIN_FRAMES    = 8
SMOOTH_ALPHA  = 0.7  # smoothing factor for bounding box

# ==== SETUP ====
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Try to import MediaPipe, fallback gracefully if not available
try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
    mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
        static_image_mode=False,
        refine_landmarks=True,
        max_num_faces=1,
        min_detection_confidence=0.4,
        min_tracking_confidence=0.4
    )
except ImportError:
    HAS_MEDIAPIPE = False
    mp_face_mesh = None
    print("Warning: MediaPipe not available. Using fallback detection.")

# ==== FUNCTIONS ====
def enhance_contrast(gray):
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    return clahe.apply(gray)

def normalise(gray):
    f = gray.astype(np.float32)
    mean, std = np.mean(f), np.std(f)
    std = std if std > 1e-6 else 1.0
    f = (f - mean) / std
    f = (f - f.min()) / (f.max() - f.min() + 1e-6)
    return (f * 255).astype(np.uint8)

def detect_lips_mediapipe(frame):
    """Detect lips using MediaPipe and return bounding box (x1,y1,x2,y2)."""
    if not HAS_MEDIAPIPE or mp_face_mesh is None:
        return None
        
    h, w, _ = frame.shape
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = mp_face_mesh.process(rgb)
    if not results.multi_face_landmarks:
        return None

    lm = results.multi_face_landmarks[0].landmark
    lip_ids = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
               78, 95, 88, 178, 87, 14, 317, 402, 318, 324]
    pts = np.array([[lm[i].x * w, lm[i].y * h] for i in lip_ids])
    x1, y1 = pts.min(axis=0)
    x2, y2 = pts.max(axis=0)
    # adaptive padding
    pad_x = (x2 - x1) * 0.3
    pad_y = (y2 - y1) * 0.4
    x1 = max(0, int(x1 - pad_x))
    y1 = max(0, int(y1 - pad_y))
    x2 = min(w, int(x2 + pad_x))
    y2 = min(h, int(y2 + pad_y))
    return [x1, y1, x2, y2]

def detect_lips_fallback(frame):
    """Fallback lip detection using color analysis and geometric assumptions."""
    h, w, _ = frame.shape
    
    # Focus on lower half of frame
    lower_half = frame[h//2:, :]
    hsv_lower = cv2.cvtColor(lower_half, cv2.COLOR_BGR2HSV)
    
    # Red/pink lip detection
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([160, 50, 50])
    upper_red2 = np.array([180, 255, 255])
    
    mask1 = cv2.inRange(hsv_lower, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv_lower, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)
    
    # Find contours
    contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    
    if contours:
        largest_contour = max(contours, key=cv2.contourArea)
        if cv2.contourArea(largest_contour) > 100:
            x, y, w_cont, h_cont = cv2.boundingRect(largest_contour)
            # Adjust coordinates back to full frame
            y += h // 2
            # Add padding
            pad_x = int(w_cont * 0.3)
            pad_y = int(h_cont * 0.4)
            x1 = max(0, x - pad_x)
            y1 = max(0, y - pad_y)
            x2 = min(w, x + w_cont + pad_x)
            y2 = min(h, y + h_cont + pad_y)
            return [x1, y1, x2, y2]
    
    # Final geometric fallback
    center_x, center_y = w // 2, int(h * 0.75)
    x1 = max(0, center_x - 60)
    y1 = max(0, center_y - 40)
    x2 = min(w, center_x + 60)
    y2 = min(h, center_y + 40)
    return [x1, y1, x2, y2]

def detect_lips(frame):
    """Main lip detection function with fallback."""
    # Try MediaPipe first
    bbox = detect_lips_mediapipe(frame)
    if bbox is not None:
        return bbox
    
    # Fallback to color-based detection
    return detect_lips_fallback(frame)

def smooth_bbox(prev, curr):
    """Exponential moving average smoothing for bounding box."""
    if prev is None:
        return curr
    return [int(SMOOTH_ALPHA*c + (1-SMOOTH_ALPHA)*p) for p, c in zip(prev, curr)]

def crop_and_resize(frame, bbox):
    """Crop using bbox and resize to target, keeping aspect ratio."""
    x1, y1, x2, y2 = bbox
    crop = frame[y1:y2, x1:x2]
    if crop.size == 0:
        # Emergency fallback
        h, w, _ = frame.shape
        crop = frame[h//2:, w//4:3*w//4]
    
    gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
    # keep aspect ratio
    h, w = gray.shape
    scale = min(TARGET_SIZE[0]/w, TARGET_SIZE[1]/h)
    new_w, new_h = int(w*scale), int(h*scale)
    resized = cv2.resize(gray, (new_w, new_h))
    canvas = np.full(TARGET_SIZE[::-1], 128, dtype=np.uint8)
    x_off = (TARGET_SIZE[0] - new_w)//2
    y_off = (TARGET_SIZE[1] - new_h)//2
    canvas[y_off:y_off+new_h, x_off:x_off+new_w] = resized
    return canvas

def sample_frames(cap, total):
    idxs = np.linspace(0, total-1, TARGET_FRAMES).astype(int)
    frames = []
    for i in idxs:
        cap.set(cv2.CAP_PROP_POS_FRAMES, i)
        ret, f = cap.read()
        if ret: frames.append(f)
    return frames

def process_video(src, dst):
    cap = cv2.VideoCapture(src)
    if not cap.isOpened():
        print(f"❌ Cannot open {src}")
        return False
    total = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total < MIN_FRAMES:
        print(f"⚠️ Too few frames {src}")
        cap.release(); return False

    frames = sample_frames(cap, total)
    cap.release()
    if not frames: 
        print(f"⚠️ No frames {src}")
        return False

    # detect once per video
    bbox = detect_lips(frames[0])
    if bbox is None:
        print(f"⚠️ No lips found {os.path.basename(src)}")
        return False

    smoothed = bbox
    processed = []
    for f in frames:
        new_bbox = detect_lips(f) or bbox
        smoothed = smooth_bbox(smoothed, new_bbox)
        roi = crop_and_resize(f, smoothed)
        roi = enhance_contrast(roi)
        roi = normalise(roi)
        processed.append(roi)

    os.makedirs(os.path.dirname(dst), exist_ok=True)
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(dst, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)
    for f in processed: out.write(f)
    out.release()
    if os.path.getsize(dst) > 500:
        print(f"✅ {os.path.basename(src)}")
        return True
    else:
        print(f"⚠️ Failed output {os.path.basename(src)}")
        return False

# ==== MAIN ====
if __name__ == "__main__":
    videos = []
    for ext in ("*.mp4", "*.MP4"):
        videos += glob(os.path.join(INPUT_DIR, "**", ext), recursive=True)
    print(f"Found {len(videos)} videos")

    success = 0
    for v in tqdm(videos, desc="Processing videos"):
        rel = os.path.relpath(v, INPUT_DIR)
        dst = os.path.join(OUTPUT_DIR, rel)
        dst = os.path.splitext(dst)[0] + ".mp4"
        if process_video(v, dst): success += 1

    print(f"\n✅ Done: {success}/{len(videos)} processed successfully.")
