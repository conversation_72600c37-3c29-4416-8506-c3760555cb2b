"""
ROI Cropping Script for ICU Lipreading Project
Crops mouth region from videos to 96x64 resolution following SRAVI pipeline
"""

import cv2
import numpy as np
import dlib
import argparse
from pathlib import Path
from typing import Tuple, Optional, List
import sys
import os

# Add utils to path
sys.path.append(str(Path(__file__).parent.parent))
from utils.config import get_config

class ROICropper:
    """Crops mouth region from video frames"""
    
    def __init__(self, target_size: Tuple[int, int] = (96, 64)):
        self.target_size = target_size  # (width, height)
        self.detector = dlib.get_frontal_face_detector()
        
        # Try to load shape predictor
        predictor_path = self._find_shape_predictor()
        if predictor_path:
            self.predictor = dlib.shape_predictor(str(predictor_path))
        else:
            print("⚠️  Shape predictor not found. Using fallback mouth detection.")
            self.predictor = None
    
    def _find_shape_predictor(self) -> Optional[Path]:
        """Find dlib shape predictor file"""
        possible_paths = [
            Path("models/pretrained/shape_predictor_68_face_landmarks.dat"),
            Path("/opt/homebrew/share/dlib/shape_predictor_68_face_landmarks.dat"),
            Path("~/.dlib/shape_predictor_68_face_landmarks.dat").expanduser(),
        ]
        
        for path in possible_paths:
            if path.exists():
                return path
        return None
    
    def detect_mouth_region(self, frame: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """
        Detect mouth region in frame
        Returns: (x, y, width, height) or None if not found
        """
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY) if len(frame.shape) == 3 else frame
        faces = self.detector(gray)
        
        if len(faces) == 0:
            return None
        
        # Use largest face
        face = max(faces, key=lambda f: f.width() * f.height())
        
        if self.predictor is not None:
            # Use landmark detection for precise mouth region
            landmarks = self.predictor(gray, face)
            
            # Mouth landmarks are points 48-67
            mouth_points = []
            for i in range(48, 68):
                point = landmarks.part(i)
                mouth_points.append((point.x, point.y))
            
            mouth_points = np.array(mouth_points)
            x_min, y_min = mouth_points.min(axis=0)
            x_max, y_max = mouth_points.max(axis=0)
            
            # Add padding
            padding = 20
            x_min = max(0, x_min - padding)
            y_min = max(0, y_min - padding)
            x_max = min(frame.shape[1], x_max + padding)
            y_max = min(frame.shape[0], y_max + padding)
            
            return (x_min, y_min, x_max - x_min, y_max - y_min)
        else:
            # Fallback: use lower third of face
            x, y, w, h = face.left(), face.top(), face.width(), face.height()
            mouth_y = y + int(h * 0.6)
            mouth_h = int(h * 0.4)
            mouth_x = x + int(w * 0.2)
            mouth_w = int(w * 0.6)
            
            return (mouth_x, mouth_y, mouth_w, mouth_h)
    
    def crop_and_resize(self, frame: np.ndarray, roi: Tuple[int, int, int, int]) -> np.ndarray:
        """Crop ROI and resize to target size"""
        x, y, w, h = roi
        cropped = frame[y:y+h, x:x+w]
        
        # Resize to target size
        resized = cv2.resize(cropped, self.target_size, interpolation=cv2.INTER_CUBIC)
        
        return resized
    
    def process_video(self, input_path: Path, output_path: Path) -> bool:
        """Process entire video file"""
        cap = cv2.VideoCapture(str(input_path))
        
        if not cap.isOpened():
            print(f"❌ Error opening video: {input_path}")
            return False
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Setup output video writer
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, self.target_size, isColor=False)
        
        processed_frames = 0
        roi_cache = None
        
        print(f"🎬 Processing {input_path.name}: {frame_count} frames at {fps:.1f} FPS")
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Detect mouth region (use cache for stability)
            if roi_cache is None or processed_frames % 10 == 0:
                roi = self.detect_mouth_region(frame)
                if roi is not None:
                    roi_cache = roi
            
            if roi_cache is not None:
                # Crop and convert to grayscale
                cropped = self.crop_and_resize(frame, roi_cache)
                if len(cropped.shape) == 3:
                    cropped = cv2.cvtColor(cropped, cv2.COLOR_BGR2GRAY)
                
                out.write(cropped)
                processed_frames += 1
            
            if processed_frames % 100 == 0:
                print(f"  Processed {processed_frames}/{frame_count} frames")
        
        cap.release()
        out.release()
        
        print(f"✅ Completed: {processed_frames} frames processed")
        return processed_frames > 0

def main():
    parser = argparse.ArgumentParser(description="Crop mouth ROI from videos")
    parser.add_argument("--input", "-i", type=str, required=True, help="Input video file or directory")
    parser.add_argument("--output", "-o", type=str, required=True, help="Output directory")
    parser.add_argument("--size", nargs=2, type=int, default=[96, 64], help="Target size (width height)")
    
    args = parser.parse_args()
    
    config = get_config()
    cropper = ROICropper(target_size=tuple(args.size))
    
    input_path = Path(args.input)
    output_path = Path(args.output)
    output_path.mkdir(parents=True, exist_ok=True)
    
    if input_path.is_file():
        # Process single file
        output_file = output_path / f"cropped_{input_path.name}"
        success = cropper.process_video(input_path, output_file)
        if success:
            print(f"✅ Successfully processed: {output_file}")
        else:
            print(f"❌ Failed to process: {input_path}")
    
    elif input_path.is_dir():
        # Process all videos in directory
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv'}
        video_files = [f for f in input_path.iterdir() 
                      if f.suffix.lower() in video_extensions]
        
        print(f"🎯 Found {len(video_files)} video files to process")
        
        for video_file in video_files:
            output_file = output_path / f"cropped_{video_file.name}"
            print(f"\n📹 Processing: {video_file.name}")
            
            success = cropper.process_video(video_file, output_file)
            if not success:
                print(f"❌ Failed to process: {video_file}")
    
    else:
        print(f"❌ Input path not found: {input_path}")

if __name__ == "__main__":
    main()
