import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm

# Try to import optional dependencies
try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
    print("✅ MediaPipe loaded successfully!")
except ImportError:
    HAS_MEDIAPIPE = False
    print("⚠️ MediaPipe not available, using fallback face detection")

try:
    from skimage import exposure
    HAS_SKIMAGE = True
except ImportError:
    HAS_SKIMAGE = False
    print("⚠️ scikit-image not available, using basic histogram matching")

try:
    from ultralytics import YOLO
    HAS_YOLO = True
except ImportError:
    HAS_YOLO = False
    print("⚠️ YOLO not available, using basic face detection")

# ==== CONFIG ====
import argparse
import sys

# Default values
INPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker"
OUTPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/processed/test_clips"
TARGET_SIZE = (96, 64)       # width, height
TARGET_FRAMES = 24
TARGET_FPS = 15

# Normalization constants for consistent standardization
PIXEL_MEAN = 127.5  # Middle of 0-255 range
PIXEL_STD = 127.5   # Half of 0-255 range
TARGET_MEAN = 0.0   # Normalized mean
TARGET_STD = 1.0    # Normalized std

# Statistical normalization parameters (computed from training data)
GLOBAL_MEAN = 0.485  # ImageNet-style normalization
GLOBAL_STD = 0.229

os.makedirs(OUTPUT_ROOT, exist_ok=True)

# ==== Face Detection Setup ====
if HAS_MEDIAPIPE:
    # Primary detector with higher confidence for clear videos
    mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
        static_image_mode=False,
        refine_landmarks=False,  # Faster processing
        max_num_faces=1,
        min_detection_confidence=0.5,
        min_tracking_confidence=0.3
    )

    # Fallback detector with lower confidence for synthetic/difficult videos
    mp_face_mesh_fallback = mp.solutions.face_mesh.FaceMesh(
        static_image_mode=True,  # More thorough detection
        refine_landmarks=True,   # Better landmark accuracy
        max_num_faces=1,
        min_detection_confidence=0.3,  # Lower threshold
        min_tracking_confidence=0.2
    )
else:
    # Fallback: use OpenCV's face detector
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# Initialize YOLO model if available
if HAS_YOLO:
    yolo_model = YOLO("yolov8n.pt")  # Will download automatically on first use

# ==== Histogram Reference ====
reference_hist = None
def compute_ref_hist(frame):
    global reference_hist
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    reference_hist, _ = np.histogram(gray.flatten(), 256, [0, 256])

def match_histogram(frame):
    if reference_hist is None:
        compute_ref_hist(frame)
        return frame
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    if HAS_SKIMAGE:
        matched = exposure.match_histograms(gray, reference_hist, channel_axis=None)
        return matched
    else:
        # Simple histogram equalization fallback
        return cv2.equalizeHist(gray)

# ==== Advanced Lip Detection ====
def find_mouth_center_of_mass(frame):
    """Find mouth using center of mass of red/pink regions"""
    h, w, _ = frame.shape

    # Convert to different color spaces
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    b, g, r = cv2.split(frame)

    # Create a more refined lip mask
    # Method 1: Red dominance (lips are typically redder than surrounding skin)
    red_dominance = np.where(r > g + 15, r - g, 0).astype(np.uint8)

    # Method 2: HSV-based detection for red/pink hues
    lower_red1 = np.array([0, 30, 30])
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([165, 30, 30])
    upper_red2 = np.array([180, 255, 255])

    hsv_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    hsv_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    hsv_mask = cv2.bitwise_or(hsv_mask1, hsv_mask2)

    # Combine masks with weights
    combined = cv2.addWeighted(red_dominance, 0.7, hsv_mask, 0.3, 0)

    # Apply Gaussian blur to smooth the mask
    combined = cv2.GaussianBlur(combined, (7, 7), 0)

    # Find center of mass
    moments = cv2.moments(combined)
    if moments["m00"] == 0:
        return None

    cx = int(moments["m10"] / moments["m00"])
    cy = int(moments["m01"] / moments["m00"])

    # Create bounding box around center of mass
    box_w = int(w * 0.3)  # 30% of frame width
    box_h = int(h * 0.25)  # 25% of frame height

    x_min = max(0, cx - box_w // 2)
    x_max = min(w, cx + box_w // 2)
    y_min = max(0, cy - box_h // 2)
    y_max = min(h, cy + box_h // 2)

    return (x_min, y_min, x_max, y_max)

def detect_mouth_edges(frame):
    """Detect mouth using edge detection focused on horizontal lines"""
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Detect edges
    edges = cv2.Canny(blurred, 50, 150)

    # Focus on horizontal edges (mouth opening)
    kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 3))
    horizontal_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_horizontal)

    # Find contours
    contours, _ = cv2.findContours(horizontal_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        return None

    # Filter contours by aspect ratio (mouth is wider than tall)
    mouth_contours = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio > 1.5 and cv2.contourArea(contour) > 100:  # Mouth is wider than tall
            mouth_contours.append(contour)

    if not mouth_contours:
        return None

    # Take the largest qualifying contour
    largest_contour = max(mouth_contours, key=cv2.contourArea)
    x, y, w, h = cv2.boundingRect(largest_contour)

    return (x, y, x + w, y + h)

# ==== MediaPipe Mouth Detection ====
def extract_mouth_roi_mediapipe(frame):
    """Extract mouth ROI using MediaPipe face mesh - softened for synthetic data"""
    if not HAS_MEDIAPIPE:
        return None

    try:
        # Initialize MediaPipe Face Mesh with relaxed settings for synthetic data
        mp_face_mesh = mp.solutions.face_mesh

        with mp_face_mesh.FaceMesh(
            static_image_mode=True,
            max_num_faces=1,
            refine_landmarks=False,  # Disable refinement for synthetic data
            min_detection_confidence=0.3,  # Lower confidence threshold
            min_tracking_confidence=0.3
        ) as face_mesh:

            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = face_mesh.process(rgb_frame)

            if not results.multi_face_landmarks:
                return None

            # Get the first face landmarks
            face_landmarks = results.multi_face_landmarks[0]
            h, w, _ = frame.shape

            # Simplified mouth landmark indices - just key points
            MOUTH_LANDMARKS = [
                # Key mouth points only
                61, 291, 39, 181, 0, 17, 18, 200, 199, 175,
                # Corner points
                78, 308, 13, 14, 269, 270
            ]

            # Extract mouth landmark coordinates
            mouth_points = []
            for idx in MOUTH_LANDMARKS:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = int(landmark.x * w)
                    y = int(landmark.y * h)
                    # Validate coordinates are reasonable
                    if 0 <= x < w and 0 <= y < h:
                        mouth_points.append((x, y))

            if len(mouth_points) < 4:  # Need at least 4 points for a reasonable box
                return None

            # Calculate bounding box around mouth landmarks
            xs = [p[0] for p in mouth_points]
            ys = [p[1] for p in mouth_points]

            x_min, x_max = min(xs), max(xs)
            y_min, y_max = min(ys), max(ys)

            # Validate bounding box is reasonable
            box_width = x_max - x_min
            box_height = y_max - y_min

            if box_width < 20 or box_height < 10:  # Too small
                return None
            if box_width > w * 0.8 or box_height > h * 0.8:  # Too large
                return None

            # Add conservative padding around mouth
            padding_x = int(box_width * 0.4)  # Increased padding
            padding_y = int(box_height * 0.4)

            x_min = max(0, x_min - padding_x)
            y_min = max(0, y_min - padding_y)
            x_max = min(w, x_max + padding_x)
            y_max = min(h, y_max + padding_y)

            # Extract mouth region
            mouth_crop = frame[y_min:y_max, x_min:x_max]

            if mouth_crop.size == 0 or mouth_crop.shape[0] < 10 or mouth_crop.shape[1] < 10:
                return None

            return mouth_crop

    except Exception as e:
        # Silently fail for problematic frames
        return None

def extract_mouth_roi(frame):
    """
    Extract mouth ROI using MediaPipe first, with fallback to geometric cropping
    """
    # Try MediaPipe first
    if HAS_MEDIAPIPE:
        mouth_crop = extract_mouth_roi_mediapipe(frame)
        if mouth_crop is not None:
            # Resize to target size
            try:
                mouth_resized = cv2.resize(mouth_crop, TARGET_SIZE)
                # Convert to grayscale
                if len(mouth_resized.shape) == 3:
                    mouth_gray = cv2.cvtColor(mouth_resized, cv2.COLOR_BGR2GRAY)
                else:
                    mouth_gray = mouth_resized
                return mouth_gray
            except:
                pass

    # Fallback to geometric cropping
    h, w, _ = frame.shape

    # Crop bottom 40% of frame, center 60% horizontally
    crop_height = int(h * 0.4)
    crop_width = int(w * 0.6)

    start_y = h - crop_height
    start_x = (w - crop_width) // 2

    end_y = h
    end_x = start_x + crop_width

    # Ensure boundaries are valid
    start_y = max(0, start_y)
    start_x = max(0, start_x)
    end_y = min(h, end_y)
    end_x = min(w, end_x)

    # Extract the crop
    crop = frame[start_y:end_y, start_x:end_x]

    if crop.size == 0 or crop.shape[0] == 0 or crop.shape[1] == 0:
        crop = frame

    # Resize to target size
    try:
        mouth_resized = cv2.resize(crop, TARGET_SIZE)
    except:
        return None

    # Convert to grayscale
    if len(mouth_resized.shape) == 3:
        mouth_gray = cv2.cvtColor(mouth_resized, cv2.COLOR_BGR2GRAY)
    else:
        mouth_gray = mouth_resized

    if mouth_gray.size == 0:
        return None

    return mouth_gray

def detect_video_type(frame):
    """Detect if this is a pre-cropped lower face or full face video"""
    h, w, _ = frame.shape

    # Method 1: Check aspect ratio - pre-cropped videos are often more rectangular
    aspect_ratio = w / h

    # Method 2: Check if we can detect eyes in upper portion
    upper_third = frame[:h//3, :]
    gray_upper = cv2.cvtColor(upper_third, cv2.COLOR_BGR2GRAY)

    # Try to detect eyes using Haar cascade
    eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
    eyes = eye_cascade.detectMultiScale(gray_upper, 1.1, 4)

    # Method 3: Check color distribution - full faces have more varied colors
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    color_variance = np.var(hsv[:, :, 1])  # Saturation variance

    # Decision logic
    has_eyes = len(eyes) > 0
    high_color_variance = color_variance > 500
    normal_aspect = 0.7 < aspect_ratio < 1.5

    if has_eyes and high_color_variance and normal_aspect:
        return "full_face"
    else:
        return "pre_cropped"

def extract_from_precropped(frame):
    """Extract mouth from pre-cropped lower face videos (grid speakers)"""
    h, w, _ = frame.shape

    # For pre-cropped videos, the mouth is typically in the center-bottom area
    # We want to crop to focus on the mouth region while maintaining standardization

    # Find the most likely mouth location using color analysis
    mouth_center = find_mouth_center_of_mass(frame)
    if mouth_center:
        x1, y1, x2, y2 = mouth_center
        mouth_center = ((x1 + x2) // 2, (y1 + y2) // 2)
    else:
        mouth_center = None

    if mouth_center is None:
        # Fallback: assume mouth is in bottom 60% of the cropped face
        mouth_center = (w // 2, int(h * 0.7))

    # Create crop that puts mouth in standard position
    # For pre-cropped faces, we need a smaller crop area
    crop_w = min(w, int(TARGET_SIZE[0] * 1.5))
    crop_h = min(h, int(TARGET_SIZE[1] * 1.5))

    # Center the crop around the mouth
    crop_x1 = max(0, mouth_center[0] - crop_w // 2)
    crop_y1 = max(0, mouth_center[1] - crop_h // 2)
    crop_x2 = min(w, crop_x1 + crop_w)
    crop_y2 = min(h, crop_y1 + crop_h)

    # Adjust if crop goes out of bounds
    if crop_x2 - crop_x1 < crop_w:
        crop_x1 = max(0, crop_x2 - crop_w)
    if crop_y2 - crop_y1 < crop_h:
        crop_y1 = max(0, crop_y2 - crop_h)

    crop = frame[crop_y1:crop_y2, crop_x1:crop_x2]

    if crop.size == 0:
        return None

    # Resize and convert to grayscale
    mouth_resized = cv2.resize(crop, TARGET_SIZE)
    if len(mouth_resized.shape) == 3:
        mouth_gray = cv2.cvtColor(mouth_resized, cv2.COLOR_BGR2GRAY)
    else:
        mouth_gray = mouth_resized

    return mouth_gray

def extract_from_fullface(frame):
    """Extract mouth from full face videos (training speakers)"""
    h, w, _ = frame.shape

    # Find mouth location using multiple methods
    mouth_center = find_mouth_location(frame)

    if mouth_center is None:
        # Fallback for full faces: mouth is typically in lower third
        mouth_center = (w // 2, int(h * 0.75))

    # Create larger crop around mouth for full faces
    crop_w = int(TARGET_SIZE[0] * 2.5)
    crop_h = int(TARGET_SIZE[1] * 2.5)

    # Position crop so mouth ends up in standard location
    target_mouth_x_ratio = 0.5  # Center horizontally
    target_mouth_y_ratio = 0.7  # 70% down vertically

    crop_x1 = mouth_center[0] - int(crop_w * target_mouth_x_ratio)
    crop_y1 = mouth_center[1] - int(crop_h * target_mouth_y_ratio)
    crop_x2 = crop_x1 + crop_w
    crop_y2 = crop_y1 + crop_h

    # Ensure crop is within frame boundaries
    crop_x1 = max(0, crop_x1)
    crop_y1 = max(0, crop_y1)
    crop_x2 = min(w, crop_x2)
    crop_y2 = min(h, crop_y2)

    crop = frame[crop_y1:crop_y2, crop_x1:crop_x2]

    if crop.size == 0:
        return None

    # Resize and convert to grayscale
    mouth_resized = cv2.resize(crop, TARGET_SIZE)
    if len(mouth_resized.shape) == 3:
        mouth_gray = cv2.cvtColor(mouth_resized, cv2.COLOR_BGR2GRAY)
    else:
        mouth_gray = mouth_resized

    return mouth_gray

def find_mouth_location(frame):
    """Find the center point of the mouth using multiple methods"""
    h, w, _ = frame.shape

    # Method 1: MediaPipe (most accurate)
    if HAS_MEDIAPIPE:
        results = mp_face_mesh.process(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        if results.multi_face_landmarks:
            landmarks = results.multi_face_landmarks[0].landmark
            # Get mouth landmarks (points around the lips)
            mouth_points = [landmarks[i] for i in [13, 14, 15, 16, 17, 18, 200, 199, 175, 0]]

            # Calculate center of mouth
            mouth_x = sum(p.x * w for p in mouth_points) / len(mouth_points)
            mouth_y = sum(p.y * h for p in mouth_points) / len(mouth_points)
            return (int(mouth_x), int(mouth_y))

    # Method 2: Center of mass of red regions
    mouth_box = find_mouth_center_of_mass(frame)
    if mouth_box:
        x1, y1, x2, y2 = mouth_box
        return ((x1 + x2) // 2, (y1 + y2) // 2)

    # Method 3: Edge detection
    mouth_box = detect_mouth_edges(frame)
    if mouth_box:
        x1, y1, x2, y2 = mouth_box
        return ((x1 + x2) // 2, (y1 + y2) // 2)

    return None

def normalize_frame_orientation(frame):
    """
    Normalize frame orientation and handle common video encoding issues
    """
    if frame is None or frame.size == 0:
        return frame

    # Ensure frame is in correct orientation (not rotated)
    h, w = frame.shape[:2]

    # If frame is significantly wider than tall, it's likely correct orientation
    # If it's taller than wide, it might be rotated
    if h > w * 1.5:  # Portrait orientation, might need rotation
        # Check if this looks like a rotated landscape video
        # For now, we'll keep it as-is but could add rotation logic here
        pass

    # Ensure frame has consistent color channels
    if len(frame.shape) == 3 and frame.shape[2] == 3:
        # Convert BGR to RGB if needed (OpenCV uses BGR by default)
        # For face detection, we'll work with BGR as MediaPipe expects RGB
        pass
    elif len(frame.shape) == 2:
        # Convert grayscale to BGR for consistent processing
        frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

    return frame

def standardize_video_properties(cap):
    """
    Standardize video properties for consistent processing across all video types
    """
    # Get original properties
    original_fps = cap.get(cv2.CAP_PROP_FPS)
    total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

    # Standardize FPS - handle invalid values
    if original_fps <= 0 or original_fps > 120 or np.isnan(original_fps):
        # Detect video type and assign appropriate FPS
        if total_frames > 0:
            # Estimate FPS based on typical video lengths
            if total_frames < 100:  # Short clip, likely high FPS
                standard_fps = 30.0
            elif total_frames < 500:  # Medium clip
                standard_fps = 25.0
            else:  # Long clip, likely standard FPS
                standard_fps = 24.0
        else:
            standard_fps = 25.0  # Default fallback
    else:
        standard_fps = original_fps

    # Standardize resolution expectations
    if width <= 0 or height <= 0:
        width, height = 640, 480  # Default resolution

    return {
        'fps': standard_fps,
        'total_frames': total_frames,
        'width': width,
        'height': height,
        'duration': total_frames / standard_fps if standard_fps > 0 else 1.6
    }

def normalize_pixel_values(frame):
    """
    Normalize pixel values to standard range with consistent statistics
    """
    # Ensure frame is float32 for precise calculations
    frame = frame.astype(np.float32)

    # Method 1: Standard normalization (0-255 → -1 to 1)
    frame_normalized = (frame - PIXEL_MEAN) / PIXEL_STD

    return frame_normalized

def standardize_contrast(frame):
    """
    Apply consistent contrast enhancement across all videos
    """
    # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))

    if len(frame.shape) == 2:  # Grayscale
        frame_enhanced = clahe.apply(frame)
    else:  # Color
        # Convert to LAB color space for better contrast enhancement
        lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)
        lab[:,:,0] = clahe.apply(lab[:,:,0])  # Apply to L channel only
        frame_enhanced = cv2.cvtColor(lab, cv2.COLOR_LAB2BGR)

    return frame_enhanced

def statistical_normalization(frame):
    """
    Apply statistical normalization for consistent mean/std across all videos
    """
    frame = frame.astype(np.float32)

    # Calculate current statistics
    current_mean = np.mean(frame)
    current_std = np.std(frame)

    # Avoid division by zero
    if current_std < 1e-6:
        current_std = 1.0

    # Normalize to target statistics
    frame_normalized = (frame - current_mean) / current_std
    frame_normalized = frame_normalized * TARGET_STD + TARGET_MEAN

    return frame_normalized

def detect_video_type(frame):
    """
    Detect if video is real or synthetic based on frame characteristics
    """
    # Convert to grayscale for analysis
    if len(frame.shape) == 3:
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    else:
        gray = frame

    # Calculate image statistics
    mean_intensity = np.mean(gray)
    std_intensity = np.std(gray)

    # Calculate edge density (synthetic videos often have sharper edges)
    edges = cv2.Canny(gray, 50, 150)
    edge_density = np.sum(edges > 0) / edges.size

    # Calculate texture complexity
    laplacian_var = cv2.Laplacian(gray, cv2.CV_64F).var()

    # Heuristic classification (can be refined with more data)
    if edge_density > 0.1 and laplacian_var > 500:
        return "synthetic"
    else:
        return "real"

def apply_comprehensive_normalization(frame, video_type=None):
    """
    Apply all normalization steps consistently across real and synthetic videos
    """
    # Auto-detect video type if not provided
    if video_type is None:
        video_type = detect_video_type(frame)

    # Step 1: Pre-processing based on video type
    if video_type == "synthetic":
        # Synthetic videos may need smoothing to reduce artificial sharpness
        frame = cv2.GaussianBlur(frame, (3, 3), 0.5)
    else:
        # Real videos may need noise reduction
        frame = cv2.bilateralFilter(frame, 5, 50, 50)

    # Step 2: Contrast enhancement (on original 0-255 range)
    frame = standardize_contrast(frame)

    # Step 3: Convert to grayscale consistently
    if len(frame.shape) == 3:
        frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Step 4: Ensure consistent size
    frame = cv2.resize(frame, TARGET_SIZE, interpolation=cv2.INTER_LANCZOS4)

    # Step 5: Pixel value normalization
    frame = normalize_pixel_values(frame)

    # Step 6: Statistical normalization
    frame = statistical_normalization(frame)

    # Step 7: Clip to reasonable range to prevent outliers
    frame = np.clip(frame, -3.0, 3.0)

    # Step 8: Final standardization - convert to consistent 0-255 range
    frame_min, frame_max = frame.min(), frame.max()
    if frame_max > frame_min:
        frame_normalized = (frame - frame_min) / (frame_max - frame_min)
    else:
        frame_normalized = np.zeros_like(frame)

    frame_uint8 = (frame_normalized * 255).astype(np.uint8)

    # Step 9: Final quality check - ensure no extreme values
    frame_uint8 = np.clip(frame_uint8, 0, 255)

    return frame_uint8

def resample_frames(frames, target_len=TARGET_FRAMES):
    if len(frames) == 0:
        return []
    idx = np.linspace(0, len(frames) - 1, target_len).astype(int)
    return [frames[i] for i in idx]

# ==== Quality Validation ====
def validate_frames(frames, video_path):
    """Validate frame quality and decide whether to include video"""
    if len(frames) == 0:
        return False, "No frames extracted"

    # Check for too many black/empty frames
    black_frames = 0
    for frame in frames:
        if np.mean(frame) < 15:  # Very dark frame
            black_frames += 1

    if black_frames > len(frames) * 0.7:  # More than 70% black frames
        return False, f"Too many black frames ({black_frames}/{len(frames)})"

    # Check for reasonable variation (not all identical frames)
    if len(frames) > 3:
        frame_diffs = []
        for i in range(1, min(len(frames), 10)):  # Check first 10 frames
            diff = np.mean(np.abs(frames[i].astype(float) - frames[i-1].astype(float)))
            frame_diffs.append(diff)

        avg_diff = np.mean(frame_diffs)
        if avg_diff < 0.5:  # Frames are too similar (static image)
            return False, f"Frames too similar (avg diff: {avg_diff:.2f})"

    return True, "OK"

# ==== Main Processing ====
def process_video(video_path, out_path):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Cannot open {video_path}")
        return

    # Standardize video properties for consistent processing
    video_props = standardize_video_properties(cap)

    # Calculate frame sampling strategy using standardized properties
    target_duration = 1.6  # seconds (24 frames at 15 FPS)
    frames_to_extract = min(video_props['total_frames'], int(video_props['fps'] * target_duration))

    # Ensure minimum frames for meaningful processing
    frames_to_extract = max(frames_to_extract, TARGET_FRAMES)

    # Create frame indices to sample uniformly across the video
    if video_props['total_frames'] > 0:
        frame_indices = np.linspace(0, video_props['total_frames'] - 1, frames_to_extract).astype(int)
    else:
        frame_indices = []

    frames = []
    total_frames_read = 0
    successful_extractions = 0

    for frame_idx in frame_indices:
        # Seek to specific frame
        cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
        ret, frame = cap.read()
        if not ret:
            continue

        total_frames_read += 1

        # Handle frame orientation issues
        frame = normalize_frame_orientation(frame)

        roi_gray = extract_mouth_roi(frame)
        if roi_gray is None:
            continue

        successful_extractions += 1

        # Apply comprehensive normalization pipeline
        roi_normalized = apply_comprehensive_normalization(roi_gray)
        frames.append(roi_normalized)
    cap.release()

    # Calculate success rate
    success_rate = successful_extractions / max(total_frames_read, 1)

    # Validate minimum requirements
    if len(frames) < 8:
        print(f"⚠️ Skipped {os.path.basename(video_path)}: Too few frames ({len(frames)})")
        return

    if success_rate < 0.2:  # Less than 20% success rate
        print(f"⚠️ Skipped {os.path.basename(video_path)}: Low success rate ({success_rate:.1%})")
        return

    # Validate frame quality
    is_valid, reason = validate_frames(frames, video_path)
    if not is_valid:
        print(f"⚠️ Skipped {os.path.basename(video_path)}: {reason}")
        return

    frames = resample_frames(frames)
    out_dir = os.path.dirname(out_path)
    os.makedirs(out_dir, exist_ok=True)
    out_path = out_path.replace(".mov", ".mp4").replace(".mpg", ".mp4")

    # Use consistent video encoding settings for all outputs
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')  # Consistent codec
    out = cv2.VideoWriter(out_path, fourcc, TARGET_FPS, TARGET_SIZE, False)

    # Ensure all frames are written with consistent properties
    for f in frames:
        # Final validation - ensure frame is correct size and type
        if f.shape != TARGET_SIZE[::-1]:  # OpenCV uses (height, width)
            f = cv2.resize(f, TARGET_SIZE)
        if f.dtype != np.uint8:
            f = f.astype(np.uint8)
        out.write(f)
    out.release()

    # Final validation - check output file
    if os.path.exists(out_path) and os.path.getsize(out_path) > 1000:
        print(f"✅ Processed: {out_path}")
    else:
        print(f"⚠️ Output validation failed: {os.path.basename(video_path)}")
        if os.path.exists(out_path):
            os.remove(out_path)  # Remove failed output

# ==== Runner ====
def main():
    global INPUT_ROOT, OUTPUT_ROOT

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Preprocess videos for lipreading")
    parser.add_argument("--input", type=str, help="Input directory")
    parser.add_argument("--output", type=str, help="Output directory")
    args = parser.parse_args()

    if args.input:
        INPUT_ROOT = args.input
    if args.output:
        OUTPUT_ROOT = args.output

    os.makedirs(OUTPUT_ROOT, exist_ok=True)

    video_files = []
    for ext in ("*.mp4", "*.mov", "*.mpg"):
        video_files += glob(os.path.join(INPUT_ROOT, "**", ext), recursive=True)
    print(f"Found {len(video_files)} videos")

    for path in tqdm(video_files):
        rel = os.path.relpath(path, INPUT_ROOT)
        out_path = os.path.join(OUTPUT_ROOT, rel)
        process_video(path, out_path)

if __name__ == "__main__":
    main()
