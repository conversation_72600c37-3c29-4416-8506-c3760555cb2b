#!/usr/bin/env python3
"""
Video preprocessing script for ICU Lipreading Project.
Stable mouth ROI extraction with smooth bounding box transitions.
"""

import os
import sys
import argparse
import cv2
import numpy as np
from pathlib import Path
from tqdm import tqdm
import logging
from typing import Optional, Tuple, List, Dict, Any
from dataclasses import dataclass
from collections import deque

# Optional imports with fallbacks
try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
except ImportError:
    HAS_MEDIAPIPE = False
    print("Warning: MediaPipe not available. Using fallback detectors only.")

try:
    from ultralytics import YOLO
    HAS_YOLO = True
except ImportError:
    HAS_YOLO = False

try:
    from skimage.exposure import match_histograms
    HAS_SKIMAGE = True
except ImportError:
    HAS_SKIMAGE = False

# Constants
TARGET_SIZE = (96, 64)  # width, height
TARGET_FPS = 15
TARGET_FRAMES = 24
OUTPUT_FORMAT = 'mp4v'

# Similarity thresholds
SYNTHETIC_SIMILARITY_THRESHOLD = 1.5
REAL_SIMILARITY_THRESHOLD = 4.0

# Smoothing parameters
BBOX_SMOOTHING_ALPHA = 0.7  # Exponential moving average factor
MAX_SCALE_CHANGE = 0.2      # Maximum scale change per frame (20%)
MAX_POSITION_CHANGE = 0.15  # Maximum position change per frame (15% of frame)

@dataclass
class BoundingBox:
    """Represents a bounding box with smoothing capabilities."""
    x: float
    y: float
    width: float
    height: float
    confidence: float = 1.0

    def center(self) -> Tuple[float, float]:
        return (self.x + self.width / 2, self.y + self.height / 2)

    def area(self) -> float:
        return self.width * self.height

    def to_xyxy(self) -> Tuple[int, int, int, int]:
        """Convert to (x1, y1, x2, y2) format."""
        return (
            int(self.x),
            int(self.y),
            int(self.x + self.width),
            int(self.y + self.height)
        )

class BBoxSmoother:
    """Smooths bounding boxes using exponential moving average with constraints."""

    def __init__(self, alpha: float = BBOX_SMOOTHING_ALPHA,
                 max_scale_change: float = MAX_SCALE_CHANGE,
                 max_position_change: float = MAX_POSITION_CHANGE):
        self.alpha = alpha
        self.max_scale_change = max_scale_change
        self.max_position_change = max_position_change
        self.previous_bbox: Optional[BoundingBox] = None
        self.frame_width = 1
        self.frame_height = 1

    def set_frame_size(self, width: int, height: int):
        """Set frame dimensions for position change calculations."""
        self.frame_width = width
        self.frame_height = height

    def smooth(self, bbox: BoundingBox) -> BoundingBox:
        """Apply smoothing to bounding box."""
        if self.previous_bbox is None:
            self.previous_bbox = bbox
            return bbox

        prev = self.previous_bbox

        # Calculate maximum allowed changes
        max_pos_change_x = self.max_position_change * self.frame_width
        max_pos_change_y = self.max_position_change * self.frame_height
        max_scale_change_w = self.max_scale_change * prev.width
        max_scale_change_h = self.max_scale_change * prev.height

        # Constrain position changes
        new_x = bbox.x
        new_y = bbox.y
        if abs(new_x - prev.x) > max_pos_change_x:
            new_x = prev.x + np.sign(new_x - prev.x) * max_pos_change_x
        if abs(new_y - prev.y) > max_pos_change_y:
            new_y = prev.y + np.sign(new_y - prev.y) * max_pos_change_y

        # Constrain scale changes
        new_width = bbox.width
        new_height = bbox.height
        if abs(new_width - prev.width) > max_scale_change_w:
            new_width = prev.width + np.sign(new_width - prev.width) * max_scale_change_w
        if abs(new_height - prev.height) > max_scale_change_h:
            new_height = prev.height + np.sign(new_height - prev.height) * max_scale_change_h

        # Apply exponential moving average
        smoothed_bbox = BoundingBox(
            x=self.alpha * new_x + (1 - self.alpha) * prev.x,
            y=self.alpha * new_y + (1 - self.alpha) * prev.y,
            width=self.alpha * new_width + (1 - self.alpha) * prev.width,
            height=self.alpha * new_height + (1 - self.alpha) * prev.height,
            confidence=bbox.confidence
        )

        self.previous_bbox = smoothed_bbox
        return smoothed_bbox

class VideoNormalizer:
    """Handles video normalization with histogram matching."""

    def __init__(self):
        self.reference_frame: Optional[np.ndarray] = None

    def set_reference(self, frame: np.ndarray):
        """Set the reference frame for histogram matching."""
        if len(frame.shape) == 3:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        self.reference_frame = frame.copy()

    def normalize_frame(self, frame: np.ndarray) -> np.ndarray:
        """Normalize frame using histogram equalization or matching."""
        if len(frame.shape) == 3:
            frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

        # Use histogram matching if available and reference is set
        if HAS_SKIMAGE and self.reference_frame is not None:
            try:
                # Ensure both frames have the same size for histogram matching
                if frame.shape != self.reference_frame.shape:
                    # Resize frame to match reference
                    frame_resized = cv2.resize(frame, (self.reference_frame.shape[1], self.reference_frame.shape[0]))
                    normalized = match_histograms(frame_resized, self.reference_frame)
                    # Resize back to original size
                    normalized = cv2.resize(normalized, (frame.shape[1], frame.shape[0]))
                else:
                    normalized = match_histograms(frame, self.reference_frame)
                return normalized.astype(np.uint8)
            except Exception as e:
                logging.debug(f"Histogram matching failed: {e}")
                pass

        # Fallback to histogram equalization
        return cv2.equalizeHist(frame)

class MouthDetector:
    """Base class for mouth detection methods."""

    def __init__(self):
        self.name = "BaseDetector"

    def detect(self, frame: np.ndarray) -> Optional[BoundingBox]:
        """Detect mouth region in frame. Returns None if not found."""
        raise NotImplementedError

class MediaPipeMouthDetector(MouthDetector):
    """MediaPipe-based mouth detection."""

    def __init__(self):
        super().__init__()
        self.name = "MediaPipe"
        if not HAS_MEDIAPIPE:
            raise ImportError("MediaPipe not available")

        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )

        # Mouth landmark indices (inner and outer lips)
        self.mouth_indices = [
            # Outer lips
            61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
            # Inner lips
            78, 95, 88, 178, 87, 14, 317, 402, 318, 324
        ]

    def detect(self, frame: np.ndarray) -> Optional[BoundingBox]:
        """Detect mouth using MediaPipe Face Mesh."""
        try:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_frame)

            if not results.multi_face_landmarks:
                return None

            face_landmarks = results.multi_face_landmarks[0]
            h, w = frame.shape[:2]

            # Extract mouth landmarks
            mouth_points = []
            for idx in self.mouth_indices:
                landmark = face_landmarks.landmark[idx]
                mouth_points.append([landmark.x * w, landmark.y * h])

            mouth_points = np.array(mouth_points)

            # Calculate bounding box
            x_min, y_min = mouth_points.min(axis=0)
            x_max, y_max = mouth_points.max(axis=0)

            # Add padding (20% on each side)
            padding_x = (x_max - x_min) * 0.2
            padding_y = (y_max - y_min) * 0.2

            x_min = max(0, x_min - padding_x)
            y_min = max(0, y_min - padding_y)
            x_max = min(w, x_max + padding_x)
            y_max = min(h, y_max + padding_y)

            return BoundingBox(
                x=x_min,
                y=y_min,
                width=x_max - x_min,
                height=y_max - y_min,
                confidence=0.9
            )

        except Exception as e:
            logging.debug(f"MediaPipe detection failed: {e}")
            return None

class YOLOFaceDetector(MouthDetector):
    """YOLO-based face detection with mouth region estimation."""

    def __init__(self):
        super().__init__()
        self.name = "YOLO"
        if not HAS_YOLO:
            raise ImportError("YOLO not available")

        try:
            self.model = YOLO('yolov8n.pt')  # Lightweight model
        except Exception as e:
            logging.warning(f"Failed to load YOLO model: {e}")
            raise

    def detect(self, frame: np.ndarray) -> Optional[BoundingBox]:
        """Detect face and estimate mouth region."""
        try:
            results = self.model(frame, verbose=False)

            for result in results:
                boxes = result.boxes
                if boxes is None:
                    continue

                for box in boxes:
                    # Check if it's a person (class 0)
                    if int(box.cls[0]) == 0 and float(box.conf[0]) > 0.5:
                        x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()

                        # Estimate mouth region (lower third of face)
                        face_height = y2 - y1
                        face_width = x2 - x1

                        mouth_y = y1 + face_height * 0.65  # 65% down from top
                        mouth_height = face_height * 0.25   # 25% of face height
                        mouth_width = face_width * 0.4      # 40% of face width
                        mouth_x = x1 + (face_width - mouth_width) / 2

                        return BoundingBox(
                            x=mouth_x,
                            y=mouth_y,
                            width=mouth_width,
                            height=mouth_height,
                            confidence=float(box.conf[0]) * 0.7  # Lower confidence for estimation
                        )

            return None

        except Exception as e:
            logging.debug(f"YOLO detection failed: {e}")
            return None

class ColorEdgeMouthDetector(MouthDetector):
    """Color and edge-based mouth detection focusing on red dominance."""

    def __init__(self):
        super().__init__()
        self.name = "ColorEdge"

    def detect(self, frame: np.ndarray) -> Optional[BoundingBox]:
        """Detect mouth using color and edge information."""
        try:
            h, w = frame.shape[:2]

            # Convert to different color spaces
            hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
            lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)

            # Focus on lower half of frame (where mouth typically is)
            roi_y_start = int(h * 0.4)
            roi_y_end = int(h * 0.9)
            roi_x_start = int(w * 0.2)
            roi_x_end = int(w * 0.8)

            roi_hsv = hsv[roi_y_start:roi_y_end, roi_x_start:roi_x_end]
            roi_lab = lab[roi_y_start:roi_y_end, roi_x_start:roi_x_end]

            # Red dominance detection
            # In HSV: red is around 0-10 and 170-180
            red_mask1 = cv2.inRange(roi_hsv, (0, 50, 50), (10, 255, 255))
            red_mask2 = cv2.inRange(roi_hsv, (170, 50, 50), (180, 255, 255))
            red_mask = cv2.bitwise_or(red_mask1, red_mask2)

            # In LAB: high A channel indicates red
            a_channel = roi_lab[:, :, 1]
            red_lab_mask = cv2.threshold(a_channel, 135, 255, cv2.THRESH_BINARY)[1]

            # Combine masks
            combined_mask = cv2.bitwise_or(red_mask, red_lab_mask)

            # Morphological operations to clean up
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 3))
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
            combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

            # Find contours
            contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return None

            # Find the largest contour in the central region
            best_contour = None
            best_score = 0

            for contour in contours:
                area = cv2.contourArea(contour)
                if area < 100:  # Too small
                    continue

                x, y, w_c, h_c = cv2.boundingRect(contour)

                # Prefer contours in the center horizontally
                center_x = x + w_c / 2
                roi_center_x = roi_x_end - roi_x_start
                distance_from_center = abs(center_x - roi_center_x / 2) / (roi_center_x / 2)

                # Score based on area and centrality
                score = area * (1 - distance_from_center * 0.5)

                if score > best_score:
                    best_score = score
                    best_contour = contour

            if best_contour is None:
                return None

            # Get bounding box and convert back to full frame coordinates
            x, y, w_c, h_c = cv2.boundingRect(best_contour)

            # Add padding
            padding_x = w_c * 0.3
            padding_y = h_c * 0.3

            final_x = max(0, roi_x_start + x - padding_x)
            final_y = max(0, roi_y_start + y - padding_y)
            final_w = min(w - final_x, w_c + 2 * padding_x)
            final_h = min(h - final_y, h_c + 2 * padding_y)

            return BoundingBox(
                x=final_x,
                y=final_y,
                width=final_w,
                height=final_h,
                confidence=0.5
            )

        except Exception as e:
            logging.debug(f"Color-edge detection failed: {e}")
            return None

class GeometricMouthDetector(MouthDetector):
    """Geometric fallback detector using face proportions."""

    def __init__(self):
        super().__init__()
        self.name = "Geometric"

    def detect(self, frame: np.ndarray) -> Optional[BoundingBox]:
        """Estimate mouth position using geometric priors."""
        h, w = frame.shape[:2]

        # Mouth is typically in lower-center of face
        # Assume face occupies central 60% of frame
        face_width = w * 0.6
        face_height = h * 0.8
        face_x = (w - face_width) / 2
        face_y = h * 0.1

        # Mouth position within face (70% down, center horizontally)
        mouth_center_x = face_x + face_width / 2
        mouth_center_y = face_y + face_height * 0.7

        # Mouth dimensions (30% of face width, 20% of face height)
        mouth_width = face_width * 0.3
        mouth_height = face_height * 0.2

        mouth_x = mouth_center_x - mouth_width / 2
        mouth_y = mouth_center_y - mouth_height / 2

        # Ensure bounds
        mouth_x = max(0, min(w - mouth_width, mouth_x))
        mouth_y = max(0, min(h - mouth_height, mouth_y))
        mouth_width = min(w - mouth_x, mouth_width)
        mouth_height = min(h - mouth_y, mouth_height)

        return BoundingBox(
            x=mouth_x,
            y=mouth_y,
            width=mouth_width,
            height=mouth_height,
            confidence=0.3  # Low confidence for geometric estimation
        )

class LayeredMouthDetector:
    """Combines multiple detection methods with fallbacks."""

    def __init__(self):
        self.detectors = []
        self.smoother = BBoxSmoother()

        # Initialize available detectors
        if HAS_MEDIAPIPE:
            try:
                self.detectors.append(MediaPipeMouthDetector())
                logging.info("MediaPipe detector initialized")
            except Exception as e:
                logging.warning(f"Failed to initialize MediaPipe: {e}")

        if HAS_YOLO:
            try:
                self.detectors.append(YOLOFaceDetector())
                logging.info("YOLO detector initialized")
            except Exception as e:
                logging.warning(f"Failed to initialize YOLO: {e}")

        # Always add color-edge and geometric detectors
        self.detectors.append(ColorEdgeMouthDetector())
        self.detectors.append(GeometricMouthDetector())

        logging.info(f"Initialized {len(self.detectors)} detectors: {[d.name for d in self.detectors]}")

    def detect_mouth(self, frame: np.ndarray) -> BoundingBox:
        """Detect mouth using layered approach with smoothing."""
        h, w = frame.shape[:2]
        self.smoother.set_frame_size(w, h)

        # Try each detector in order
        for detector in self.detectors:
            try:
                bbox = detector.detect(frame)
                if bbox is not None:
                    # Apply smoothing
                    smoothed_bbox = self.smoother.smooth(bbox)
                    logging.debug(f"Detection successful with {detector.name}")
                    return smoothed_bbox
            except Exception as e:
                logging.debug(f"Detector {detector.name} failed: {e}")
                continue

        # This should never happen since GeometricMouthDetector always returns a bbox
        # But just in case, return a default
        return BoundingBox(
            x=w * 0.35, y=h * 0.6,
            width=w * 0.3, height=h * 0.2,
            confidence=0.1
        )

def is_synthetic_video(video_path: str) -> bool:
    """Determine if video is synthetic based on filename patterns."""
    filename = Path(video_path).stem.lower()

    # Synthetic video patterns
    synthetic_patterns = [
        'doctor__', 'help__', 'phone__', 'pillow__', 'my_mouth_is_dry__', 'i_need_to_move__',
        '__useruser', '__18to39__', '__40to64__', '__65plus__',
        '__male__', '__female__', '__caucasian__', '__aboriginal__'
    ]

    return any(pattern in filename for pattern in synthetic_patterns)

def calculate_frame_similarity(frames: List[np.ndarray]) -> float:
    """Calculate average frame-to-frame difference."""
    if len(frames) < 2:
        return 0.0

    total_diff = 0.0
    count = 0

    for i in range(1, len(frames)):
        try:
            # Convert to grayscale if needed
            frame1 = frames[i-1]
            frame2 = frames[i]

            if len(frame1.shape) == 3:
                frame1 = cv2.cvtColor(frame1, cv2.COLOR_BGR2GRAY)
            if len(frame2.shape) == 3:
                frame2 = cv2.cvtColor(frame2, cv2.COLOR_BGR2GRAY)

            # Ensure frames have the same size
            if frame1.shape != frame2.shape:
                # Resize frame2 to match frame1
                frame2 = cv2.resize(frame2, (frame1.shape[1], frame1.shape[0]))

            # Calculate mean absolute difference
            diff = np.mean(np.abs(frame1.astype(np.float32) - frame2.astype(np.float32)))
            total_diff += diff
            count += 1
        except Exception as e:
            logging.debug(f"Error calculating similarity between frames {i-1} and {i}: {e}")
            continue

    return total_diff / count if count > 0 else 0.0

class VideoProcessor:
    """Main video processing class with stable mouth ROI extraction."""

    def __init__(self):
        self.mouth_detector = LayeredMouthDetector()
        self.normalizer = VideoNormalizer()

    def extract_frames(self, video_path: str, target_frames: int = TARGET_FRAMES) -> List[np.ndarray]:
        """Extract evenly spaced frames from video with robust error handling."""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            raise ValueError(f"Cannot open video: {video_path}")

        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames < target_frames:
            cap.release()
            raise ValueError(f"Video has only {total_frames} frames, need at least {target_frames}")

        # Calculate frame indices to extract with some buffer
        frame_indices = np.linspace(0, total_frames - 1, target_frames * 2, dtype=int)

        frames = []
        attempts = 0
        max_attempts = len(frame_indices)

        for frame_idx in frame_indices:
            if len(frames) >= target_frames:
                break

            attempts += 1
            if attempts > max_attempts:
                break

            try:
                cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
                ret, frame = cap.read()
                if ret and frame is not None and frame.size > 0:
                    # Validate frame dimensions
                    if len(frame.shape) >= 2 and frame.shape[0] > 0 and frame.shape[1] > 0:
                        frames.append(frame)
                    else:
                        logging.debug(f"Invalid frame dimensions at {frame_idx}: {frame.shape}")
                else:
                    logging.debug(f"Failed to read frame {frame_idx} from {video_path}")
            except Exception as e:
                logging.debug(f"Error reading frame {frame_idx}: {e}")
                continue

        cap.release()

        if len(frames) < target_frames:
            raise ValueError(f"Could only extract {len(frames)} frames from {video_path}, need {target_frames}")

        # Return exactly target_frames
        return frames[:target_frames]

    def process_video(self, video_path: str, output_path: str) -> bool:
        """Process a single video file."""
        try:
            logging.info(f"Processing: {video_path}")

            # Extract frames
            frames = self.extract_frames(video_path)
            logging.debug(f"Extracted {len(frames)} frames")

            # Set reference frame for normalization (first frame)
            self.normalizer.set_reference(frames[0])

            # Process each frame
            processed_frames = []
            mouth_regions = []  # Store original mouth regions for similarity check

            for i, frame in enumerate(frames):
                # Detect mouth region
                mouth_bbox = self.mouth_detector.detect_mouth(frame)

                # Extract mouth ROI
                x1, y1, x2, y2 = mouth_bbox.to_xyxy()
                mouth_roi = frame[y1:y2, x1:x2]

                if mouth_roi.size == 0:
                    logging.warning(f"Empty mouth ROI in frame {i}")
                    continue

                # Convert to grayscale
                if len(mouth_roi.shape) == 3:
                    mouth_gray = cv2.cvtColor(mouth_roi, cv2.COLOR_BGR2GRAY)
                else:
                    mouth_gray = mouth_roi

                # Store original for similarity check
                mouth_regions.append(mouth_gray.copy())

                # Normalize
                normalized = self.normalizer.normalize_frame(mouth_gray)

                # Resize to target size
                resized = cv2.resize(normalized, TARGET_SIZE)
                processed_frames.append(resized)

            if len(processed_frames) < TARGET_FRAMES:
                logging.warning(f"Only processed {len(processed_frames)} frames, expected {TARGET_FRAMES}")
                return False

            # Check frame similarity using original mouth regions (before normalization)
            avg_diff = calculate_frame_similarity(mouth_regions)
            is_synthetic = is_synthetic_video(video_path)

            threshold = SYNTHETIC_SIMILARITY_THRESHOLD if is_synthetic else REAL_SIMILARITY_THRESHOLD

            if avg_diff < threshold:
                logging.warning(f"Video too similar (avg_diff: {avg_diff:.2f} < {threshold:.2f}): {video_path}")
                return False

            logging.debug(f"Frame similarity check passed (avg_diff: {avg_diff:.2f})")

            # Save processed video
            self.save_video(processed_frames, output_path)
            logging.info(f"✅ Processed: {output_path}")
            return True

        except Exception as e:
            logging.error(f"Failed to process {video_path}: {e}")
            return False

    def save_video(self, frames: List[np.ndarray], output_path: str):
        """Save processed frames as video."""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        fourcc = cv2.VideoWriter_fourcc(*OUTPUT_FORMAT)
        out = cv2.VideoWriter(output_path, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)

        for frame in frames:
            out.write(frame)

        out.release()

def setup_logging(verbose: bool = False):
    """Setup logging configuration."""
    level = logging.DEBUG if verbose else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Preprocess videos for lipreading')
    parser.add_argument('--input', required=True, help='Input directory containing videos')
    parser.add_argument('--output', required=True, help='Output directory for processed videos')
    parser.add_argument('--verbose', '-v', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    setup_logging(args.verbose)

    input_dir = Path(args.input)
    output_dir = Path(args.output)

    if not input_dir.exists():
        logging.error(f"Input directory does not exist: {input_dir}")
        sys.exit(1)

    # Find all video files
    video_extensions = {'.mp4', '.avi', '.mov', '.MOV', '.mpg', '.mpeg'}
    video_files = []

    for ext in video_extensions:
        video_files.extend(input_dir.glob(f'*{ext}'))

    if not video_files:
        logging.error(f"No video files found in {input_dir}")
        sys.exit(1)

    logging.info(f"Found {len(video_files)} video files")

    # Process videos
    processor = VideoProcessor()
    successful = 0
    failed = 0

    for video_file in tqdm(video_files, desc="Processing videos"):
        output_file = output_dir / f"{video_file.stem}.mp4"

        if processor.process_video(str(video_file), str(output_file)):
            successful += 1
        else:
            failed += 1
            logging.warning(f"❌ Failed: {video_file.name}")

    logging.info(f"Processing complete: {successful} successful, {failed} failed")

if __name__ == "__main__":
    main()