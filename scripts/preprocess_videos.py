import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm

# Try to import optional dependencies
try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
except ImportError:
    HAS_MEDIAPIPE = False
    print("⚠️ MediaPipe not available, using fallback face detection")

try:
    from skimage import exposure
    HAS_SKIMAGE = True
except ImportError:
    HAS_SKIMAGE = False
    print("⚠️ scikit-image not available, using basic histogram matching")

try:
    from ultralytics import YOLO
    HAS_YOLO = True
except ImportError:
    HAS_YOLO = False
    print("⚠️ YOLO not available, using basic face detection")

# ==== CONFIG ====
import argparse
import sys

# Default values
INPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker"
OUTPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/processed/test_clips"
TARGET_SIZE = (96, 64)       # width, height
TARGET_FRAMES = 24
TARGET_FPS = 15

os.makedirs(OUTPUT_ROOT, exist_ok=True)

# ==== Face Detection Setup ====
if HAS_MEDIAPIPE:
    mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
        static_image_mode=False,
        refine_landmarks=True,
        max_num_faces=1,
        min_detection_confidence=0.5
    )
else:
    # Fallback: use OpenCV's face detector
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# Initialize YOLO model if available
if HAS_YOLO:
    yolo_model = YOLO("yolov8n.pt")  # Will download automatically on first use

# ==== Histogram Reference ====
reference_hist = None
def compute_ref_hist(frame):
    global reference_hist
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    reference_hist, _ = np.histogram(gray.flatten(), 256, [0, 256])

def match_histogram(frame):
    if reference_hist is None:
        compute_ref_hist(frame)
        return frame
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    if HAS_SKIMAGE:
        matched = exposure.match_histograms(gray, reference_hist, channel_axis=None)
        return matched
    else:
        # Simple histogram equalization fallback
        return cv2.equalizeHist(gray)

# ==== Advanced Lip Detection ====
def find_mouth_center_of_mass(frame):
    """Find mouth using center of mass of red/pink regions"""
    h, w, _ = frame.shape

    # Convert to different color spaces
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    b, g, r = cv2.split(frame)

    # Create a more refined lip mask
    # Method 1: Red dominance (lips are typically redder than surrounding skin)
    red_dominance = np.where(r > g + 15, r - g, 0).astype(np.uint8)

    # Method 2: HSV-based detection for red/pink hues
    lower_red1 = np.array([0, 30, 30])
    upper_red1 = np.array([15, 255, 255])
    lower_red2 = np.array([165, 30, 30])
    upper_red2 = np.array([180, 255, 255])

    hsv_mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    hsv_mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    hsv_mask = cv2.bitwise_or(hsv_mask1, hsv_mask2)

    # Combine masks with weights
    combined = cv2.addWeighted(red_dominance, 0.7, hsv_mask, 0.3, 0)

    # Apply Gaussian blur to smooth the mask
    combined = cv2.GaussianBlur(combined, (7, 7), 0)

    # Find center of mass
    moments = cv2.moments(combined)
    if moments["m00"] == 0:
        return None

    cx = int(moments["m10"] / moments["m00"])
    cy = int(moments["m01"] / moments["m00"])

    # Create bounding box around center of mass
    box_w = int(w * 0.3)  # 30% of frame width
    box_h = int(h * 0.25)  # 25% of frame height

    x_min = max(0, cx - box_w // 2)
    x_max = min(w, cx + box_w // 2)
    y_min = max(0, cy - box_h // 2)
    y_max = min(h, cy + box_h // 2)

    return (x_min, y_min, x_max, y_max)

def detect_mouth_edges(frame):
    """Detect mouth using edge detection focused on horizontal lines"""
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Apply Gaussian blur
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)

    # Detect edges
    edges = cv2.Canny(blurred, 50, 150)

    # Focus on horizontal edges (mouth opening)
    kernel_horizontal = cv2.getStructuringElement(cv2.MORPH_RECT, (15, 3))
    horizontal_edges = cv2.morphologyEx(edges, cv2.MORPH_CLOSE, kernel_horizontal)

    # Find contours
    contours, _ = cv2.findContours(horizontal_edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        return None

    # Filter contours by aspect ratio (mouth is wider than tall)
    mouth_contours = []
    for contour in contours:
        x, y, w, h = cv2.boundingRect(contour)
        aspect_ratio = w / h if h > 0 else 0
        if aspect_ratio > 1.5 and cv2.contourArea(contour) > 100:  # Mouth is wider than tall
            mouth_contours.append(contour)

    if not mouth_contours:
        return None

    # Take the largest qualifying contour
    largest_contour = max(mouth_contours, key=cv2.contourArea)
    x, y, w, h = cv2.boundingRect(largest_contour)

    return (x, y, x + w, y + h)

# ==== OpenCV-Only Mouth Detection ====
def detect_mouth_opencv(frame):
    """Detect mouth using only OpenCV - no external dependencies"""
    h, w, _ = frame.shape
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    # Method 1: Try face detection first, then estimate mouth location
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    faces = face_cascade.detectMultiScale(gray, 1.1, 4)

    if len(faces) > 0:
        # Take the largest face
        face = max(faces, key=lambda f: f[2] * f[3])
        fx, fy, fw, fh = face

        # Mouth is typically in the bottom third of the face
        mouth_y = fy + int(fh * 0.65)
        mouth_h = int(fh * 0.35)
        mouth_x = fx + int(fw * 0.2)
        mouth_w = int(fw * 0.6)

        return (mouth_x, mouth_y, mouth_x + mouth_w, mouth_y + mouth_h)

    # Method 2: If no face detected, use color-based mouth detection
    return detect_mouth_by_color(frame)

def detect_mouth_by_color(frame):
    """Detect mouth using color analysis - lips are typically redder"""
    h, w, _ = frame.shape

    # Convert to different color spaces
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    b, g, r = cv2.split(frame)

    # Create mask for red/pink regions (typical lip colors)
    # HSV mask for red hues
    lower_red1 = np.array([0, 50, 50])
    upper_red1 = np.array([10, 255, 255])
    lower_red2 = np.array([170, 50, 50])
    upper_red2 = np.array([180, 255, 255])

    mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
    mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
    red_mask = cv2.bitwise_or(mask1, mask2)

    # RGB mask for red dominance
    red_dominance = np.where((r > g + 10) & (r > b + 10), 255, 0).astype(np.uint8)

    # Combine masks
    combined_mask = cv2.bitwise_or(red_mask, red_dominance)

    # Clean up the mask
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_CLOSE, kernel)
    combined_mask = cv2.morphologyEx(combined_mask, cv2.MORPH_OPEN, kernel)

    # Find contours
    contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    if not contours:
        return None

    # Filter contours by size and aspect ratio
    valid_contours = []
    for contour in contours:
        area = cv2.contourArea(contour)
        if area > 100:  # Minimum area threshold
            x, y, cw, ch = cv2.boundingRect(contour)
            aspect_ratio = cw / ch if ch > 0 else 0
            if 1.2 < aspect_ratio < 4.0:  # Mouth is wider than tall
                valid_contours.append(contour)

    if not valid_contours:
        return None

    # Take the largest valid contour
    largest_contour = max(valid_contours, key=cv2.contourArea)
    x, y, cw, ch = cv2.boundingRect(largest_contour)

    return (x, y, x + cw, y + ch)

def extract_mouth_roi(frame):
    """Extract mouth ROI using OpenCV-only detection"""
    h, w, _ = frame.shape

    # Detect mouth location
    mouth_box = detect_mouth_opencv(frame)

    if mouth_box is None:
        # Fallback: crop bottom-center area
        crop_h = int(h * 0.4)
        crop_w = int(w * 0.6)
        start_y = h - crop_h
        start_x = (w - crop_w) // 2
        mouth_box = (start_x, start_y, start_x + crop_w, start_y + crop_h)

    x1, y1, x2, y2 = mouth_box

    # Add padding around detected mouth
    pad_x, pad_y = 20, 15
    x1 = max(0, x1 - pad_x)
    y1 = max(0, y1 - pad_y)
    x2 = min(w, x2 + pad_x)
    y2 = min(h, y2 + pad_y)

    # Extract crop
    crop = frame[y1:y2, x1:x2]

    if crop.size == 0:
        return None

    # Resize to target size
    mouth_resized = cv2.resize(crop, TARGET_SIZE)

    # Convert to grayscale
    if len(mouth_resized.shape) == 3:
        mouth_gray = cv2.cvtColor(mouth_resized, cv2.COLOR_BGR2GRAY)
    else:
        mouth_gray = mouth_resized

    return mouth_gray

def detect_video_type(frame):
    """Detect if this is a pre-cropped lower face or full face video"""
    h, w, _ = frame.shape

    # Method 1: Check aspect ratio - pre-cropped videos are often more rectangular
    aspect_ratio = w / h

    # Method 2: Check if we can detect eyes in upper portion
    upper_third = frame[:h//3, :]
    gray_upper = cv2.cvtColor(upper_third, cv2.COLOR_BGR2GRAY)

    # Try to detect eyes using Haar cascade
    eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
    eyes = eye_cascade.detectMultiScale(gray_upper, 1.1, 4)

    # Method 3: Check color distribution - full faces have more varied colors
    hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
    color_variance = np.var(hsv[:, :, 1])  # Saturation variance

    # Decision logic
    has_eyes = len(eyes) > 0
    high_color_variance = color_variance > 500
    normal_aspect = 0.7 < aspect_ratio < 1.5

    if has_eyes and high_color_variance and normal_aspect:
        return "full_face"
    else:
        return "pre_cropped"

def extract_from_precropped(frame):
    """Extract mouth from pre-cropped lower face videos (grid speakers)"""
    h, w, _ = frame.shape

    # For pre-cropped videos, the mouth is typically in the center-bottom area
    # We want to crop to focus on the mouth region while maintaining standardization

    # Find the most likely mouth location using color analysis
    mouth_center = find_mouth_center_of_mass(frame)
    if mouth_center:
        x1, y1, x2, y2 = mouth_center
        mouth_center = ((x1 + x2) // 2, (y1 + y2) // 2)
    else:
        mouth_center = None

    if mouth_center is None:
        # Fallback: assume mouth is in bottom 60% of the cropped face
        mouth_center = (w // 2, int(h * 0.7))

    # Create crop that puts mouth in standard position
    # For pre-cropped faces, we need a smaller crop area
    crop_w = min(w, int(TARGET_SIZE[0] * 1.5))
    crop_h = min(h, int(TARGET_SIZE[1] * 1.5))

    # Center the crop around the mouth
    crop_x1 = max(0, mouth_center[0] - crop_w // 2)
    crop_y1 = max(0, mouth_center[1] - crop_h // 2)
    crop_x2 = min(w, crop_x1 + crop_w)
    crop_y2 = min(h, crop_y1 + crop_h)

    # Adjust if crop goes out of bounds
    if crop_x2 - crop_x1 < crop_w:
        crop_x1 = max(0, crop_x2 - crop_w)
    if crop_y2 - crop_y1 < crop_h:
        crop_y1 = max(0, crop_y2 - crop_h)

    crop = frame[crop_y1:crop_y2, crop_x1:crop_x2]

    if crop.size == 0:
        return None

    # Resize and convert to grayscale
    mouth_resized = cv2.resize(crop, TARGET_SIZE)
    if len(mouth_resized.shape) == 3:
        mouth_gray = cv2.cvtColor(mouth_resized, cv2.COLOR_BGR2GRAY)
    else:
        mouth_gray = mouth_resized

    return mouth_gray

def extract_from_fullface(frame):
    """Extract mouth from full face videos (training speakers)"""
    h, w, _ = frame.shape

    # Find mouth location using multiple methods
    mouth_center = find_mouth_location(frame)

    if mouth_center is None:
        # Fallback for full faces: mouth is typically in lower third
        mouth_center = (w // 2, int(h * 0.75))

    # Create larger crop around mouth for full faces
    crop_w = int(TARGET_SIZE[0] * 2.5)
    crop_h = int(TARGET_SIZE[1] * 2.5)

    # Position crop so mouth ends up in standard location
    target_mouth_x_ratio = 0.5  # Center horizontally
    target_mouth_y_ratio = 0.7  # 70% down vertically

    crop_x1 = mouth_center[0] - int(crop_w * target_mouth_x_ratio)
    crop_y1 = mouth_center[1] - int(crop_h * target_mouth_y_ratio)
    crop_x2 = crop_x1 + crop_w
    crop_y2 = crop_y1 + crop_h

    # Ensure crop is within frame boundaries
    crop_x1 = max(0, crop_x1)
    crop_y1 = max(0, crop_y1)
    crop_x2 = min(w, crop_x2)
    crop_y2 = min(h, crop_y2)

    crop = frame[crop_y1:crop_y2, crop_x1:crop_x2]

    if crop.size == 0:
        return None

    # Resize and convert to grayscale
    mouth_resized = cv2.resize(crop, TARGET_SIZE)
    if len(mouth_resized.shape) == 3:
        mouth_gray = cv2.cvtColor(mouth_resized, cv2.COLOR_BGR2GRAY)
    else:
        mouth_gray = mouth_resized

    return mouth_gray

def find_mouth_location(frame):
    """Find the center point of the mouth using multiple methods"""
    h, w, _ = frame.shape

    # Method 1: MediaPipe (most accurate)
    if HAS_MEDIAPIPE:
        results = mp_face_mesh.process(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        if results.multi_face_landmarks:
            landmarks = results.multi_face_landmarks[0].landmark
            # Get mouth landmarks (points around the lips)
            mouth_points = [landmarks[i] for i in [13, 14, 15, 16, 17, 18, 200, 199, 175, 0]]

            # Calculate center of mouth
            mouth_x = sum(p.x * w for p in mouth_points) / len(mouth_points)
            mouth_y = sum(p.y * h for p in mouth_points) / len(mouth_points)
            return (int(mouth_x), int(mouth_y))

    # Method 2: Center of mass of red regions
    mouth_box = find_mouth_center_of_mass(frame)
    if mouth_box:
        x1, y1, x2, y2 = mouth_box
        return ((x1 + x2) // 2, (y1 + y2) // 2)

    # Method 3: Edge detection
    mouth_box = detect_mouth_edges(frame)
    if mouth_box:
        x1, y1, x2, y2 = mouth_box
        return ((x1 + x2) // 2, (y1 + y2) // 2)

    return None

def resample_frames(frames, target_len=TARGET_FRAMES):
    if len(frames) == 0:
        return []
    idx = np.linspace(0, len(frames) - 1, target_len).astype(int)
    return [frames[i] for i in idx]

# ==== Main Processing ====
def process_video(video_path, out_path):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Cannot open {video_path}")
        return

    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        roi_gray = extract_mouth_roi(frame)
        if roi_gray is None:
            continue

        # Apply histogram matching if available
        if len(roi_gray.shape) == 3:
            roi_gray = match_histogram(roi_gray)
        else:
            # Already grayscale, apply histogram equalization
            roi_gray = cv2.equalizeHist(roi_gray)

        # Normalize the frame
        roi_gray = roi_gray.astype(np.float32)
        roi_gray = (roi_gray - np.mean(roi_gray)) / (np.std(roi_gray) + 1e-5)
        roi_gray = np.clip(roi_gray, -2, 2)
        roi_gray = ((roi_gray + 2) / 4 * 255).astype(np.uint8)
        frames.append(roi_gray)
    cap.release()

    if len(frames) < 8:
        print(f"⚠️ Skipped short or undetected: {video_path}")
        return

    frames = resample_frames(frames)
    out_dir = os.path.dirname(out_path)
    os.makedirs(out_dir, exist_ok=True)
    out_path = out_path.replace(".mov", ".mp4").replace(".mpg", ".mp4")

    out = cv2.VideoWriter(out_path, cv2.VideoWriter_fourcc(*'mp4v'), TARGET_FPS, TARGET_SIZE, False)
    for f in frames:
        out.write(f)
    out.release()
    print(f"✅ Processed: {out_path}")

# ==== Runner ====
def main():
    global INPUT_ROOT, OUTPUT_ROOT

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Preprocess videos for lipreading")
    parser.add_argument("--input", type=str, help="Input directory")
    parser.add_argument("--output", type=str, help="Output directory")
    args = parser.parse_args()

    if args.input:
        INPUT_ROOT = args.input
    if args.output:
        OUTPUT_ROOT = args.output

    os.makedirs(OUTPUT_ROOT, exist_ok=True)

    video_files = []
    for ext in ("*.mp4", "*.mov", "*.mpg"):
        video_files += glob(os.path.join(INPUT_ROOT, "**", ext), recursive=True)
    print(f"Found {len(video_files)} videos")

    for path in tqdm(video_files):
        rel = os.path.relpath(path, INPUT_ROOT)
        out_path = os.path.join(OUTPUT_ROOT, rel)
        process_video(path, out_path)

if __name__ == "__main__":
    main()
