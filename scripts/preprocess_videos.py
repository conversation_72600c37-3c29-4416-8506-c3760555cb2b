import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm

# Try to import optional dependencies
try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
except ImportError:
    HAS_MEDIAPIPE = False
    print("⚠️ MediaPipe not available, using fallback face detection")

try:
    from skimage import exposure
    HAS_SKIMAGE = True
except ImportError:
    HAS_SKIMAGE = False
    print("⚠️ scikit-image not available, using basic histogram matching")

# ==== CONFIG ====
import argparse
import sys

# Default values
INPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker"
OUTPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/processed/test_clips"
TARGET_SIZE = (96, 64)       # width, height
TARGET_FRAMES = 24
TARGET_FPS = 15

os.makedirs(OUTPUT_ROOT, exist_ok=True)

# ==== Face Detection Setup ====
if HAS_MEDIAPIPE:
    mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
        static_image_mode=False,
        refine_landmarks=True,
        max_num_faces=1,
        min_detection_confidence=0.5
    )
else:
    # Fallback: use OpenCV's face detector
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# ==== Histogram Reference ====
reference_hist = None
def compute_ref_hist(frame):
    global reference_hist
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    reference_hist, _ = np.histogram(gray.flatten(), 256, [0, 256])

def match_histogram(frame):
    if reference_hist is None:
        compute_ref_hist(frame)
        return frame
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    if HAS_SKIMAGE:
        matched = exposure.match_histograms(gray, reference_hist, channel_axis=None)
        return matched
    else:
        # Simple histogram equalization fallback
        return cv2.equalizeHist(gray)

# ==== Mouth ROI Extraction ====
def extract_mouth_roi(frame):
    h, w, _ = frame.shape

    if HAS_MEDIAPIPE:
        results = mp_face_mesh.process(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        if not results.multi_face_landmarks:
            return None
        landmarks = results.multi_face_landmarks[0].landmark
        mouth_points = [landmarks[i] for i in range(61, 88)]  # mouth region
        xs = [p.x * w for p in mouth_points]
        ys = [p.y * h for p in mouth_points]
        x_min, x_max = int(min(xs)), int(max(xs))
        y_min, y_max = int(min(ys)), int(max(ys))
        pad_x, pad_y = 10, 10
        x_min = max(0, x_min - pad_x)
        x_max = min(w, x_max + pad_x)
        y_min = max(0, y_min - pad_y)
        y_max = min(h, y_max + pad_y)
        return frame[y_min:y_max, x_min:x_max]
    else:
        # Fallback: use OpenCV face detection and estimate mouth region
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        if len(faces) == 0:
            return None

        # Take the largest face
        face = max(faces, key=lambda f: f[2] * f[3])
        x, y, fw, fh = face

        # Estimate mouth region (lower third of face)
        mouth_y = y + int(fh * 0.6)
        mouth_h = int(fh * 0.4)
        mouth_x = x + int(fw * 0.2)
        mouth_w = int(fw * 0.6)

        return frame[mouth_y:mouth_y+mouth_h, mouth_x:mouth_x+mouth_w]

def resample_frames(frames, target_len=TARGET_FRAMES):
    if len(frames) == 0:
        return []
    idx = np.linspace(0, len(frames) - 1, target_len).astype(int)
    return [frames[i] for i in idx]

# ==== Main Processing ====
def process_video(video_path, out_path):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Cannot open {video_path}")
        return

    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        roi = extract_mouth_roi(frame)
        if roi is None:
            continue
        roi = cv2.resize(roi, TARGET_SIZE)
        roi_gray = match_histogram(roi)
        roi_gray = (roi_gray - np.mean(roi_gray)) / (np.std(roi_gray) + 1e-5)
        roi_gray = np.clip(roi_gray, -2, 2)
        roi_gray = ((roi_gray + 2) / 4 * 255).astype(np.uint8)
        frames.append(roi_gray)
    cap.release()

    if len(frames) < 8:
        print(f"⚠️ Skipped short or undetected: {video_path}")
        return

    frames = resample_frames(frames)
    out_dir = os.path.dirname(out_path)
    os.makedirs(out_dir, exist_ok=True)
    out_path = out_path.replace(".mov", ".mp4").replace(".mpg", ".mp4")

    out = cv2.VideoWriter(out_path, cv2.VideoWriter_fourcc(*'mp4v'), TARGET_FPS, TARGET_SIZE, False)
    for f in frames:
        out.write(f)
    out.release()
    print(f"✅ Processed: {out_path}")

# ==== Runner ====
def main():
    global INPUT_ROOT, OUTPUT_ROOT

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Preprocess videos for lipreading")
    parser.add_argument("--input", type=str, help="Input directory")
    parser.add_argument("--output", type=str, help="Output directory")
    args = parser.parse_args()

    if args.input:
        INPUT_ROOT = args.input
    if args.output:
        OUTPUT_ROOT = args.output

    os.makedirs(OUTPUT_ROOT, exist_ok=True)

    video_files = []
    for ext in ("*.mp4", "*.mov", "*.mpg"):
        video_files += glob(os.path.join(INPUT_ROOT, "**", ext), recursive=True)
    print(f"Found {len(video_files)} videos")

    for path in tqdm(video_files):
        rel = os.path.relpath(path, INPUT_ROOT)
        out_path = os.path.join(OUTPUT_ROOT, rel)
        process_video(path, out_path)

if __name__ == "__main__":
    main()
