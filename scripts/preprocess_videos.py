import cv2
import mediapipe as mp
import numpy as np
import os
from glob import glob
from tqdm import tqdm
from skimage import exposure

# ==== CONFIG ====
INPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker"
OUTPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/processed/one_video_from_each_speaker"
TARGET_SIZE = (96, 64)       # width, height
TARGET_FRAMES = 24
TARGET_FPS = 15

os.makedirs(OUTPUT_ROOT, exist_ok=True)

# ==== MediaPipe FaceMesh ====
mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
    static_image_mode=False,
    refine_landmarks=True,
    max_num_faces=1,
    min_detection_confidence=0.5
)

# ==== Histogram Reference ====
reference_hist = None
def compute_ref_hist(frame):
    global reference_hist
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    reference_hist, _ = np.histogram(gray.flatten(), 256, [0, 256])

def match_histogram(frame):
    if reference_hist is None:
        compute_ref_hist(frame)
        return frame
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    matched = exposure.match_histograms(gray, reference_hist, channel_axis=None)
    return matched

# ==== Mouth ROI Extraction ====
def extract_mouth_roi(frame):
    h, w, _ = frame.shape
    results = mp_face_mesh.process(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
    if not results.multi_face_landmarks:
        return None
    landmarks = results.multi_face_landmarks[0].landmark
    mouth_points = [landmarks[i] for i in range(61, 88)]  # mouth region
    xs = [p.x * w for p in mouth_points]
    ys = [p.y * h for p in mouth_points]
    x_min, x_max = int(min(xs)), int(max(xs))
    y_min, y_max = int(min(ys)), int(max(ys))
    pad_x, pad_y = 10, 10
    x_min = max(0, x_min - pad_x)
    x_max = min(w, x_max + pad_x)
    y_min = max(0, y_min - pad_y)
    y_max = min(h, y_max + pad_y)
    return frame[y_min:y_max, x_min:x_max]

def resample_frames(frames, target_len=TARGET_FRAMES):
    if len(frames) == 0:
        return []
    idx = np.linspace(0, len(frames) - 1, target_len).astype(int)
    return [frames[i] for i in idx]

# ==== Main Processing ====
def process_video(video_path, out_path):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Cannot open {video_path}")
        return

    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        roi = extract_mouth_roi(frame)
        if roi is None:
            continue
        roi = cv2.resize(roi, TARGET_SIZE)
        roi_gray = match_histogram(roi)
        roi_gray = (roi_gray - np.mean(roi_gray)) / (np.std(roi_gray) + 1e-5)
        roi_gray = np.clip(roi_gray, -2, 2)
        roi_gray = ((roi_gray + 2) / 4 * 255).astype(np.uint8)
        frames.append(roi_gray)
    cap.release()

    if len(frames) < 8:
        print(f"⚠️ Skipped short or undetected: {video_path}")
        return

    frames = resample_frames(frames)
    out_dir = os.path.dirname(out_path)
    os.makedirs(out_dir, exist_ok=True)
    out_path = out_path.replace(".mov", ".mp4").replace(".mpg", ".mp4")

    out = cv2.VideoWriter(out_path, cv2.VideoWriter_fourcc(*'mp4v'), TARGET_FPS, TARGET_SIZE, False)
    for f in frames:
        out.write(f)
    out.release()
    print(f"✅ Processed: {out_path}")

# ==== Runner ====
def main():
    video_files = []
    for ext in ("*.mp4", "*.mov", "*.mpg"):
        video_files += glob(os.path.join(INPUT_ROOT, "**", ext), recursive=True)
    print(f"Found {len(video_files)} videos")

    for path in tqdm(video_files):
        rel = os.path.relpath(path, INPUT_ROOT)
        out_path = os.path.join(OUTPUT_ROOT, rel)
        process_video(path, out_path)

if __name__ == "__main__":
    main()
