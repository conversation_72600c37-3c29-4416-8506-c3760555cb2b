import cv2
import numpy as np
import os
from glob import glob
from tqdm import tqdm

# Try to import optional dependencies
try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
except ImportError:
    HAS_MEDIAPIPE = False
    print("⚠️ MediaPipe not available, using fallback face detection")

try:
    from skimage import exposure
    HAS_SKIMAGE = True
except ImportError:
    HAS_SKIMAGE = False
    print("⚠️ scikit-image not available, using basic histogram matching")

try:
    from ultralytics import YOLO
    HAS_YOLO = True
except ImportError:
    HAS_YOLO = False
    print("⚠️ YOLO not available, using basic face detection")

# ==== CONFIG ====
import argparse
import sys

# Default values
INPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker"
OUTPUT_ROOT = "/Users/<USER>/Desktop/LRP final/data/processed/test_clips"
TARGET_SIZE = (96, 64)       # width, height
TARGET_FRAMES = 24
TARGET_FPS = 15

os.makedirs(OUTPUT_ROOT, exist_ok=True)

# ==== Face Detection Setup ====
if HAS_MEDIAPIPE:
    mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
        static_image_mode=False,
        refine_landmarks=True,
        max_num_faces=1,
        min_detection_confidence=0.5
    )
else:
    # Fallback: use OpenCV's face detector
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')

# Initialize YOLO model if available
if HAS_YOLO:
    yolo_model = YOLO("yolov8n.pt")  # Will download automatically on first use

# ==== Histogram Reference ====
reference_hist = None
def compute_ref_hist(frame):
    global reference_hist
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
    reference_hist, _ = np.histogram(gray.flatten(), 256, [0, 256])

def match_histogram(frame):
    if reference_hist is None:
        compute_ref_hist(frame)
        return frame
    gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

    if HAS_SKIMAGE:
        matched = exposure.match_histograms(gray, reference_hist, channel_axis=None)
        return matched
    else:
        # Simple histogram equalization fallback
        return cv2.equalizeHist(gray)

# ==== Mouth ROI Extraction with Hybrid Detection ====
def extract_mouth_roi(frame):
    h, w, _ = frame.shape
    conf = 0.0
    x_min = y_min = x_max = y_max = 0

    # 1️⃣ Try MediaPipe first (most accurate when it works)
    if HAS_MEDIAPIPE:
        results = mp_face_mesh.process(cv2.cvtColor(frame, cv2.COLOR_BGR2RGB))
        if results.multi_face_landmarks:
            landmarks = results.multi_face_landmarks[0].landmark
            mouth_points = [landmarks[i] for i in range(61, 88)]  # mouth region
            xs = [p.x * w for p in mouth_points]
            ys = [p.y * h for p in mouth_points]
            x_min, x_max = int(min(xs)), int(max(xs))
            y_min, y_max = int(min(ys)), int(max(ys))
            conf = 1.0

    # 2️⃣ Fallback to YOLO if MediaPipe failed
    if conf < 0.5 and HAS_YOLO:
        try:
            dets = yolo_model(frame, verbose=False)[0].boxes
            if len(dets) > 0:
                # Take biggest detection (assuming it's a face/person)
                areas = (dets.xyxy[:,2]-dets.xyxy[:,0]) * (dets.xyxy[:,3]-dets.xyxy[:,1])
                i = int(np.argmax(areas))
                x_min, y_min, x_max, y_max = map(int, dets.xyxy[i])

                # Estimate mouth region from detected box (lower portion)
                box_h = y_max - y_min
                mouth_y_start = y_min + int(box_h * 0.6)  # Lower 40% of detection
                mouth_y_end = y_max
                y_min, y_max = mouth_y_start, mouth_y_end
                conf = 0.8
        except Exception as e:
            print(f"⚠️ YOLO detection failed: {e}")

    # 3️⃣ Add padding + retry MediaPipe if both failed
    if conf < 0.5 and HAS_MEDIAPIPE:
        pad = 80
        frame_pad = cv2.copyMakeBorder(frame, pad, pad, 0, 0,
                                       cv2.BORDER_CONSTANT, value=[0,0,0])
        results2 = mp_face_mesh.process(cv2.cvtColor(frame_pad, cv2.COLOR_BGR2RGB))
        if results2.multi_face_landmarks:
            landmarks = results2.multi_face_landmarks[0].landmark
            mouth_points = [landmarks[i] for i in range(61, 88)]
            xs = [p.x * w for p in mouth_points]
            ys = [(p.y * (h+2*pad)) - pad for p in mouth_points]
            x_min, x_max = int(min(xs)), int(max(xs))
            y_min, y_max = int(min(ys)), int(max(ys))
            conf = 0.7

    # 4️⃣ Final fallback: OpenCV face detection
    if conf < 0.5:
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)
        if len(faces) > 0:
            # Take the largest face
            face = max(faces, key=lambda f: f[2] * f[3])
            x, y, fw, fh = face

            # Estimate mouth region (lower third of face)
            mouth_y = y + int(fh * 0.6)
            mouth_h = int(fh * 0.4)
            mouth_x = x + int(fw * 0.2)
            mouth_w = int(fw * 0.6)

            x_min, y_min = mouth_x, mouth_y
            x_max, y_max = mouth_x + mouth_w, mouth_y + mouth_h
            conf = 0.6

    # Return None if no detection method worked
    if conf < 0.5:
        return None

    # 5️⃣ Expand + recenter crop
    pad_x, pad_y = 10, 10
    x_min = max(0, x_min - pad_x)
    x_max = min(w, x_max + pad_x)
    y_min = max(0, y_min - pad_y)
    y_max = min(h, y_max + pad_y)

    mouth = frame[y_min:y_max, x_min:x_max]

    if mouth.size == 0:
        return None

    # 6️⃣ Recenter lips vertically for consistent positioning
    target_h = TARGET_SIZE[1]  # 64
    current_h = mouth.shape[0]

    if current_h < target_h:
        # Add padding to center the mouth vertically
        diff = (target_h - current_h) // 2
        mouth = cv2.copyMakeBorder(mouth, diff, target_h - current_h - diff, 0, 0,
                                   cv2.BORDER_CONSTANT, value=[0,0,0])

    return mouth

def resample_frames(frames, target_len=TARGET_FRAMES):
    if len(frames) == 0:
        return []
    idx = np.linspace(0, len(frames) - 1, target_len).astype(int)
    return [frames[i] for i in idx]

# ==== Main Processing ====
def process_video(video_path, out_path):
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        print(f"❌ Cannot open {video_path}")
        return

    frames = []
    while True:
        ret, frame = cap.read()
        if not ret:
            break
        roi = extract_mouth_roi(frame)
        if roi is None:
            continue
        roi = cv2.resize(roi, TARGET_SIZE)
        roi_gray = match_histogram(roi)
        roi_gray = (roi_gray - np.mean(roi_gray)) / (np.std(roi_gray) + 1e-5)
        roi_gray = np.clip(roi_gray, -2, 2)
        roi_gray = ((roi_gray + 2) / 4 * 255).astype(np.uint8)
        frames.append(roi_gray)
    cap.release()

    if len(frames) < 8:
        print(f"⚠️ Skipped short or undetected: {video_path}")
        return

    frames = resample_frames(frames)
    out_dir = os.path.dirname(out_path)
    os.makedirs(out_dir, exist_ok=True)
    out_path = out_path.replace(".mov", ".mp4").replace(".mpg", ".mp4")

    out = cv2.VideoWriter(out_path, cv2.VideoWriter_fourcc(*'mp4v'), TARGET_FPS, TARGET_SIZE, False)
    for f in frames:
        out.write(f)
    out.release()
    print(f"✅ Processed: {out_path}")

# ==== Runner ====
def main():
    global INPUT_ROOT, OUTPUT_ROOT

    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Preprocess videos for lipreading")
    parser.add_argument("--input", type=str, help="Input directory")
    parser.add_argument("--output", type=str, help="Output directory")
    args = parser.parse_args()

    if args.input:
        INPUT_ROOT = args.input
    if args.output:
        OUTPUT_ROOT = args.output

    os.makedirs(OUTPUT_ROOT, exist_ok=True)

    video_files = []
    for ext in ("*.mp4", "*.mov", "*.mpg"):
        video_files += glob(os.path.join(INPUT_ROOT, "**", ext), recursive=True)
    print(f"Found {len(video_files)} videos")

    for path in tqdm(video_files):
        rel = os.path.relpath(path, INPUT_ROOT)
        out_path = os.path.join(OUTPUT_ROOT, rel)
        process_video(path, out_path)

if __name__ == "__main__":
    main()
