"""
Evaluation Script for ICU Lipreading Project
Implements comprehensive evaluation including cross-validation
"""

import torch
import torch.nn as nn
import numpy as np
import argparse
from pathlib import Path
import json
import sys
from tqdm import tqdm
import matplotlib.pyplot as plt
from typing import Dict, List, Any

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from utils import (
    get_config, LipreadingDataset, create_dataloader,
    MetricsCalculator, CrossValidationSplitter
)
from models.lipnet import create_model

class Evaluator:
    """Model evaluation class"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = None
        
    def load_model(self, checkpoint_path: str):
        """Load trained model from checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # Get model config from checkpoint
        if 'config' in checkpoint:
            model_config = checkpoint['config']
        else:
            model_config = self.config
        
        # Create and load model
        self.model = create_model(model_config).to(self.device)
        self.model.load_state_dict(checkpoint['model_state_dict'])
        self.model.eval()
        
        print(f"✅ Loaded model from: {checkpoint_path}")
    
    def evaluate_dataset(self, dataset: LipreadingDataset, batch_size: int = 32) -> Dict[str, Any]:
        """Evaluate model on a dataset"""
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")
        
        dataloader = create_dataloader(dataset, batch_size=batch_size, shuffle=False)
        metrics_calculator = MetricsCalculator(dataset.labels)
        
        all_predictions = []
        all_targets = []
        all_confidences = []
        all_logits = []
        
        with torch.no_grad():
            pbar = tqdm(dataloader, desc="Evaluating")
            
            for videos, targets, metadata in pbar:
                videos = videos.to(self.device)
                targets = targets.to(self.device)
                
                # Forward pass
                logits = self.model(videos)
                
                # Get predictions and confidences
                predictions = torch.argmax(logits, dim=1)
                confidences = torch.softmax(logits, dim=1).max(dim=1)[0]
                
                # Store results
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_confidences.extend(confidences.cpu().numpy())
                all_logits.extend(logits.cpu().numpy())
                
                # Update metrics
                metrics_calculator.update(predictions, targets, confidences)
        
        # Compute all metrics
        metrics = metrics_calculator.compute_all_metrics()
        
        # Add additional analysis
        results = {
            'metrics': metrics,
            'predictions': all_predictions,
            'targets': all_targets,
            'confidences': all_confidences,
            'logits': all_logits,
            'confusion_matrix': metrics_calculator.compute_confusion_matrix().tolist(),
            'classification_report': metrics_calculator.get_classification_report()
        }
        
        return results
    
    def evaluate_with_tta(self, dataset: LipreadingDataset, 
                         temporal_crops: int = 5, spatial_crops: int = 3,
                         batch_size: int = 16) -> Dict[str, Any]:
        """Evaluate with Test Time Augmentation"""
        if self.model is None:
            raise ValueError("Model not loaded. Call load_model() first.")
        
        dataloader = create_dataloader(dataset, batch_size=batch_size, shuffle=False)
        metrics_calculator = MetricsCalculator(dataset.labels)
        
        all_predictions = []
        all_targets = []
        all_confidences = []
        
        with torch.no_grad():
            pbar = tqdm(dataloader, desc="Evaluating with TTA")
            
            for videos, targets, metadata in pbar:
                videos = videos.to(self.device)
                targets = targets.to(self.device)
                
                batch_logits = []
                
                # Temporal crops
                for t_crop in range(temporal_crops):
                    # Spatial crops (center + corners)
                    for s_crop in range(spatial_crops):
                        # Apply augmentations
                        aug_videos = self.apply_tta_transform(videos, t_crop, s_crop, 
                                                            temporal_crops, spatial_crops)
                        
                        # Forward pass
                        logits = self.model(aug_videos)
                        batch_logits.append(logits)
                
                # Average predictions across all crops
                avg_logits = torch.stack(batch_logits).mean(dim=0)
                
                # Get final predictions
                predictions = torch.argmax(avg_logits, dim=1)
                confidences = torch.softmax(avg_logits, dim=1).max(dim=1)[0]
                
                # Store results
                all_predictions.extend(predictions.cpu().numpy())
                all_targets.extend(targets.cpu().numpy())
                all_confidences.extend(confidences.cpu().numpy())
                
                # Update metrics
                metrics_calculator.update(predictions, targets, confidences)
        
        # Compute metrics
        metrics = metrics_calculator.compute_all_metrics()
        
        results = {
            'metrics': metrics,
            'predictions': all_predictions,
            'targets': all_targets,
            'confidences': all_confidences,
            'tta_config': {
                'temporal_crops': temporal_crops,
                'spatial_crops': spatial_crops
            }
        }
        
        return results
    
    def apply_tta_transform(self, videos: torch.Tensor, t_crop: int, s_crop: int,
                           total_t_crops: int, total_s_crops: int) -> torch.Tensor:
        """Apply test-time augmentation transforms"""
        B, C, T, H, W = videos.shape
        
        # Temporal cropping
        if total_t_crops > 1:
            crop_size = max(1, T // total_t_crops)
            start_t = t_crop * crop_size
            end_t = min(start_t + crop_size, T)
            videos = videos[:, :, start_t:end_t, :, :]
            
            # Pad if necessary
            if videos.shape[2] < T:
                pad_size = T - videos.shape[2]
                videos = torch.cat([videos, videos[:, :, -1:].repeat(1, 1, pad_size, 1, 1)], dim=2)
        
        # Spatial cropping (center, top-left, top-right, bottom-left, bottom-right)
        if total_s_crops > 1:
            crop_h, crop_w = int(H * 0.9), int(W * 0.9)
            
            if s_crop == 0:  # Center crop
                start_h, start_w = (H - crop_h) // 2, (W - crop_w) // 2
            elif s_crop == 1:  # Top-left
                start_h, start_w = 0, 0
            elif s_crop == 2:  # Top-right
                start_h, start_w = 0, W - crop_w
            elif s_crop == 3:  # Bottom-left
                start_h, start_w = H - crop_h, 0
            else:  # Bottom-right
                start_h, start_w = H - crop_h, W - crop_w
            
            videos = videos[:, :, :, start_h:start_h+crop_h, start_w:start_w+crop_w]
            
            # Resize back to original size
            videos = torch.nn.functional.interpolate(
                videos.view(-1, C, H, W), size=(H, W), mode='bilinear', align_corners=False
            ).view(B, C, T, H, W)
        
        return videos
    
    def cross_validation_evaluation(self, manifest_path: str, n_folds: int = 5) -> Dict[str, Any]:
        """Perform cross-validation evaluation"""
        
        # Create CV splits
        cv_splitter = CrossValidationSplitter(manifest_path, n_folds=n_folds)
        splits_dir = Path("data/splits/cv_evaluation")
        fold_paths = cv_splitter.create_folds(splits_dir)
        
        fold_results = []
        
        for fold_info in fold_paths:
            fold_idx = fold_info['fold']
            print(f"\n🔄 Evaluating Fold {fold_idx}")
            
            # Load validation data for this fold
            val_dataset = LipreadingDataset(fold_info['val'])
            
            # Evaluate
            results = self.evaluate_dataset(val_dataset)
            results['fold'] = fold_idx
            fold_results.append(results)
            
            print(f"Fold {fold_idx} Results:")
            print(f"  Accuracy: {results['metrics']['accuracy']:.4f}")
            print(f"  Macro-F1: {results['metrics']['macro_f1']:.4f}")
        
        # Aggregate results across folds
        cv_metrics = self.aggregate_cv_results(fold_results)
        
        return {
            'fold_results': fold_results,
            'cv_metrics': cv_metrics,
            'n_folds': n_folds
        }
    
    def aggregate_cv_results(self, fold_results: List[Dict]) -> Dict[str, Any]:
        """Aggregate cross-validation results"""
        
        # Extract metrics from all folds
        metrics_keys = fold_results[0]['metrics'].keys()
        aggregated = {}
        
        for key in metrics_keys:
            values = [fold['metrics'][key] for fold in fold_results]
            aggregated[f"{key}_mean"] = np.mean(values)
            aggregated[f"{key}_std"] = np.std(values)
            aggregated[f"{key}_values"] = values
        
        return aggregated
    
    def save_results(self, results: Dict[str, Any], output_path: Path):
        """Save evaluation results"""
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Convert numpy arrays to lists for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj
        
        results_json = convert_numpy(results)
        
        with open(output_path, 'w') as f:
            json.dump(results_json, f, indent=2)
        
        print(f"💾 Saved results to: {output_path}")

def main():
    parser = argparse.ArgumentParser(description="Evaluate ICU Lipreading Model")
    parser.add_argument("--checkpoint", "-c", type=str, required=True, help="Model checkpoint path")
    parser.add_argument("--manifest", "-m", type=str, required=True, help="Test manifest file")
    parser.add_argument("--output", "-o", type=str, default="results/evaluation.json", help="Output file")
    parser.add_argument("--batch-size", "-b", type=int, default=32, help="Batch size")
    parser.add_argument("--tta", action="store_true", help="Use test-time augmentation")
    parser.add_argument("--cv", action="store_true", help="Perform cross-validation")
    parser.add_argument("--cv-folds", type=int, default=5, help="Number of CV folds")
    parser.add_argument("--config", type=str, help="Custom config file")
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config:
        import yaml
        with open(args.config, 'r') as f:
            config = yaml.safe_load(f)
    else:
        config = get_config().config
    
    # Initialize evaluator
    evaluator = Evaluator(config)
    evaluator.load_model(args.checkpoint)
    
    if args.cv:
        # Cross-validation evaluation
        results = evaluator.cross_validation_evaluation(args.manifest, args.cv_folds)
        print(f"\n📊 Cross-Validation Results (n={args.cv_folds}):")
        cv_metrics = results['cv_metrics']
        print(f"  Accuracy: {cv_metrics['accuracy_mean']:.4f} ± {cv_metrics['accuracy_std']:.4f}")
        print(f"  Macro-F1: {cv_metrics['macro_f1_mean']:.4f} ± {cv_metrics['macro_f1_std']:.4f}")
    else:
        # Single evaluation
        dataset = LipreadingDataset(args.manifest)
        
        if args.tta:
            results = evaluator.evaluate_with_tta(dataset, batch_size=args.batch_size)
            print("📊 Evaluation Results (with TTA):")
        else:
            results = evaluator.evaluate_dataset(dataset, batch_size=args.batch_size)
            print("📊 Evaluation Results:")
        
        metrics = results['metrics']
        print(f"  Accuracy: {metrics['accuracy']:.4f}")
        print(f"  Macro-F1: {metrics['macro_f1']:.4f}")
        print(f"  Weighted-F1: {metrics['weighted_f1']:.4f}")
        
        if 'high_confidence_accuracy' in metrics:
            print(f"  High Confidence Accuracy: {metrics['high_confidence_accuracy']:.4f}")
            print(f"  Coverage: {metrics['coverage']:.4f}")
    
    # Save results
    evaluator.save_results(results, Path(args.output))

if __name__ == "__main__":
    main()
