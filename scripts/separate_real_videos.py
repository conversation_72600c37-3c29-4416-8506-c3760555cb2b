#!/usr/bin/env python3
"""
Separate Real Videos from Synthetic
-----------------------------------
Identifies and copies real speaker videos to a separate directory,
filtering out synthetic/generated content for clean preprocessing.
"""

import os
import shutil
import argparse
from glob import glob
from pathlib import Path

def is_real_video(filename):
    """
    Determine if a video is real based on filename patterns.
    Real videos typically have:
    - UUID-style names (e.g., 9B1B750F-84EC-1791-0E1A-C54078907B23.mov)
    - Speaker descriptors (e.g., doctor_speaker_1_video_1.mov)
    - Dataset names (e.g., bbaf2s.mpg, bbal1n.mpg)
    - Simple copy variations (e.g., 2D3B1FC9-2604-4D27-948B-4FBFE2D05DF8 copy 13.MOV)

    Synthetic videos typically have:
    - "fixed_" prefix (indicating processed synthetic content)
    - Very descriptive names with many underscores and metadata
    - User recordings with complex metadata patterns
    """
    filename_lower = filename.lower()
    base_name = os.path.splitext(filename_lower)[0]

    # Patterns that indicate SYNTHETIC videos (check first - higher priority)
    synthetic_patterns = [
        # Fixed prefix (processed synthetic)
        lambda f: f.startswith('fixed_'),
        # Complex user recording patterns with lots of metadata
        lambda f: 'user' in f and f.count('__') >= 4,
        # Very long descriptive names with many underscores
        lambda f: f.count('_') > 6 and len(f) > 50,
        # Specific synthetic indicators
        lambda f: 'synthetic' in f or 'generated' in f or 'artificial' in f,
        # Specific phrases that indicate user-generated content
        lambda f: any(phrase in f for phrase in ['i_need_to_move', 'my_mouth_is_dry', 'help__user', 'phone__user', 'pillow__user']),
    ]

    # Check synthetic patterns first
    for pattern in synthetic_patterns:
        if pattern(filename_lower):
            return False

    # Patterns that indicate REAL videos
    real_patterns = [
        # UUID-style filenames (common for real recordings)
        lambda f: len([c for c in f if c in '0123456789abcdef-']) > len(f) * 0.5 and '-' in f,
        # Dataset video patterns (GRID-style)
        lambda f: f.startswith('bb') and f.endswith('.mpg'),
        # Doctor/speaker patterns
        lambda f: 'doctor' in f and 'speaker' in f and f.count('_') <= 4,
        # Simple copy variations of UUID-style names
        lambda f: 'copy' in f and len([c for c in f if c in '0123456789abcdef-']) > 20,
        # MOV files with UUID patterns
        lambda f: f.endswith('.mov') and len([c for c in f if c in '0123456789abcdef-']) > 15,
    ]

    # Check real patterns
    for pattern in real_patterns:
        if pattern(filename_lower):
            return True

    # Default: assume synthetic if uncertain (conservative approach for real-only processing)
    return False

def separate_videos(input_dir, real_output_dir, synthetic_output_dir=None):
    """Separate real and synthetic videos into different directories."""
    
    # Find all video files
    video_extensions = ("*.mp4", "*.mov", "*.MOV", "*.mpg", "*.mpeg", "*.MPEG")
    all_videos = []
    
    for ext in video_extensions:
        all_videos.extend(glob(os.path.join(input_dir, "**", ext), recursive=True))
    
    print(f"Found {len(all_videos)} total videos")
    
    # Create output directories
    os.makedirs(real_output_dir, exist_ok=True)
    if synthetic_output_dir:
        os.makedirs(synthetic_output_dir, exist_ok=True)
    
    real_count = 0
    synthetic_count = 0
    
    for video_path in all_videos:
        filename = os.path.basename(video_path)
        
        if is_real_video(filename):
            # Copy to real directory
            dest_path = os.path.join(real_output_dir, filename)
            shutil.copy2(video_path, dest_path)
            print(f"✅ REAL: {filename}")
            real_count += 1
        else:
            # Copy to synthetic directory (if specified)
            if synthetic_output_dir:
                dest_path = os.path.join(synthetic_output_dir, filename)
                shutil.copy2(video_path, dest_path)
            print(f"🤖 SYNTHETIC: {filename}")
            synthetic_count += 1
    
    print(f"\n=== Separation Complete ===")
    print(f"✅ Real videos: {real_count}")
    print(f"🤖 Synthetic videos: {synthetic_count}")
    print(f"📊 Real percentage: {real_count/(real_count+synthetic_count)*100:.1f}%")

def main():
    parser = argparse.ArgumentParser(description="Separate real videos from synthetic ones")
    parser.add_argument('--input', required=True, help='Input directory containing mixed videos')
    parser.add_argument('--real-output', required=True, help='Output directory for real videos')
    parser.add_argument('--synthetic-output', help='Output directory for synthetic videos (optional)')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be done without copying')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"Error: Input directory does not exist: {args.input}")
        return
    
    if args.dry_run:
        print("=== DRY RUN MODE - No files will be copied ===")
        # Just analyze and show results
        video_extensions = ("*.mp4", "*.mov", "*.MOV", "*.mpg", "*.mpeg", "*.MPEG")
        all_videos = []
        
        for ext in video_extensions:
            all_videos.extend(glob(os.path.join(args.input, "**", ext), recursive=True))
        
        real_count = 0
        synthetic_count = 0
        
        for video_path in all_videos:
            filename = os.path.basename(video_path)
            if is_real_video(filename):
                print(f"✅ REAL: {filename}")
                real_count += 1
            else:
                print(f"🤖 SYNTHETIC: {filename}")
                synthetic_count += 1
        
        print(f"\nWould separate:")
        print(f"✅ Real videos: {real_count}")
        print(f"🤖 Synthetic videos: {synthetic_count}")
    else:
        separate_videos(args.input, args.real_output, args.synthetic_output)

if __name__ == "__main__":
    main()
