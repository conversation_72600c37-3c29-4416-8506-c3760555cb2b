#!/usr/bin/env python3
"""
Compare Cropping Results
------------------------
Creates side-by-side comparison of different preprocessing approaches
to visually verify lip detection improvements.
"""

import cv2
import numpy as np
import os
from pathlib import Path

def create_comparison_frame(original_frame, old_crop, new_crop, title1="Old Crop", title2="New Crop"):
    """Create a side-by-side comparison frame."""
    
    # Resize original for display
    orig_display = cv2.resize(original_frame, (200, 150))
    
    # Resize crops for display (make them larger for visibility)
    old_display = cv2.resize(old_crop, (150, 100)) if old_crop is not None else np.zeros((100, 150), dtype=np.uint8)
    new_display = cv2.resize(new_crop, (150, 100)) if new_crop is not None else np.zeros((100, 150), dtype=np.uint8)
    
    # Convert grayscale to BGR for concatenation
    if len(old_display.shape) == 2:
        old_display = cv2.cvtColor(old_display, cv2.COLOR_GRAY2BGR)
    if len(new_display.shape) == 2:
        new_display = cv2.cvtColor(new_display, cv2.COLOR_GRAY2BGR)
    
    # Add text labels
    cv2.putText(orig_display, "Original", (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    cv2.putText(old_display, title1, (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    cv2.putText(new_display, title2, (10, 20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
    
    # Create comparison layout - ensure all have same width
    target_width = 400

    # Resize all components to same width
    orig_display = cv2.resize(orig_display, (target_width, 150))
    old_display = cv2.resize(old_display, (target_width//2 - 10, 100))
    new_display = cv2.resize(new_display, (target_width//2 - 10, 100))

    # Create bottom row with proper spacing
    spacer = np.zeros((100, 20, 3), dtype=np.uint8)
    bottom_row = np.hstack([old_display, spacer, new_display])

    # Ensure bottom row matches target width
    if bottom_row.shape[1] != target_width:
        bottom_row = cv2.resize(bottom_row, (target_width, 100))

    # Create final comparison
    spacer_row = np.zeros((20, target_width, 3), dtype=np.uint8)
    comparison = np.vstack([orig_display, spacer_row, bottom_row])
    
    return comparison

def compare_video_crops(original_path, old_processed_path, new_processed_path, output_path):
    """Compare crops from two different processing approaches."""
    
    # Read original video
    cap_orig = cv2.VideoCapture(original_path)
    if not cap_orig.isOpened():
        print(f"Cannot open original video: {original_path}")
        return
    
    # Read processed videos
    cap_old = cv2.VideoCapture(old_processed_path) if os.path.exists(old_processed_path) else None
    cap_new = cv2.VideoCapture(new_processed_path) if os.path.exists(new_processed_path) else None
    
    if cap_old is None and cap_new is None:
        print("No processed videos found")
        return
    
    # Get video properties
    fps = int(cap_orig.get(cv2.CAP_PROP_FPS)) or 15
    total_frames = int(cap_orig.get(cv2.CAP_PROP_FRAME_COUNT))
    
    # Create output video writer
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    out = cv2.VideoWriter(output_path, fourcc, fps, (400, 270))  # Match our target dimensions
    
    frame_count = 0
    max_frames = min(total_frames, 50)  # Limit to first 50 frames
    
    while frame_count < max_frames:
        # Read original frame
        ret_orig, frame_orig = cap_orig.read()
        if not ret_orig:
            break
        
        # Read processed frames
        old_crop = None
        new_crop = None
        
        if cap_old:
            ret_old, old_crop = cap_old.read()
            if not ret_old:
                old_crop = None
        
        if cap_new:
            ret_new, new_crop = cap_new.read()
            if not ret_new:
                new_crop = None
        
        # Create comparison frame
        comparison = create_comparison_frame(frame_orig, old_crop, new_crop, "Previous Adaptive", "Vertical Adaptive")
        
        # Resize to fit output dimensions
        comparison = cv2.resize(comparison, (400, 270))
        
        out.write(comparison)
        frame_count += 1
    
    # Cleanup
    cap_orig.release()
    if cap_old:
        cap_old.release()
    if cap_new:
        cap_new.release()
    out.release()
    
    print(f"Comparison video saved: {output_path}")

def main():
    # Paths
    original_dir = "data/raw/one_video_from_each_speaker_mp4"
    old_processed_dir = "data/processed/real_adaptive"
    new_processed_dir = "data/processed/vertical_adaptive_lips"
    comparison_dir = "data/processed/vertical_comparisons"
    
    os.makedirs(comparison_dir, exist_ok=True)
    
    # Sample videos to compare
    sample_videos = [
        "doctor_speaker 1_video 1.mp4",
        "bbaf2s.mp4",
        "doctor__useruser01__18to39__female__caucasian__20250804T033419 copy 2.mp4"
    ]
    
    for video_name in sample_videos:
        # Find corresponding files
        original_path = None
        for ext in ['.mov', '.mp4', '.mpg']:
            test_path = os.path.join(original_dir, video_name.replace('.mov', ext).replace('.mp4', ext).replace('.mpg', ext))
            if os.path.exists(test_path):
                original_path = test_path
                break
        
        if not original_path:
            print(f"Original video not found: {video_name}")
            continue
        
        # Processed paths
        base_name = os.path.splitext(video_name)[0]
        old_processed_path = os.path.join(old_processed_dir, base_name + ".mp4")
        new_processed_path = os.path.join(new_processed_dir, base_name + ".mp4")
        
        # Output path
        output_path = os.path.join(comparison_dir, f"comparison_{base_name}.mp4")
        
        print(f"Creating comparison for: {video_name}")
        compare_video_crops(original_path, old_processed_path, new_processed_path, output_path)

if __name__ == "__main__":
    main()
