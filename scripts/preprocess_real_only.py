#!/usr/bin/env python3
"""
ICU Lipreading Preprocessing (Real-Only)
----------------------------------------
Extracts stable, standardised mouth ROI from real videos only.
No synthetic logic, no full-face detection — focuses on lower half of frame.
Outputs consistent 96x64 grayscale clips (24 frames @ 15fps) for training.
"""

import os
import cv2
import numpy as np
import argparse
from glob import glob
from tqdm import tqdm
import logging

# Try to import MediaPipe, fallback gracefully if not available
try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
    mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
        static_image_mode=False,
        refine_landmarks=False,
        max_num_faces=1,
        min_detection_confidence=0.4,
        min_tracking_confidence=0.4
    )
except ImportError:
    HAS_MEDIAPIPE = False
    mp_face_mesh = None

# ==== CONFIG ====
TARGET_SIZE = (96, 64)   # (width, height)
TARGET_FRAMES = 24
TARGET_FPS = 15
MIN_FRAMES = 8  # Minimum frames required to process a video

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealVideoProcessor:
    """Processor for real ICU speaker videos only."""
    
    def __init__(self):
        self.processed_count = 0
        self.failed_count = 0
        
    def enhance_contrast(self, frame_gray):
        """Apply CLAHE for contrast enhancement."""
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        return clahe.apply(frame_gray)
    
    def normalise_frame(self, frame_gray):
        """Apply z-normalisation for pixel uniformity."""
        frame = frame_gray.astype(np.float32)
        mean, std = np.mean(frame), np.std(frame)
        std = std if std > 1e-6 else 1.0
        frame = (frame - mean) / std
        
        # Scale to 0-255 range
        if frame.max() > frame.min():
            frame = (frame - frame.min()) / (frame.max() - frame.min()) * 255
        else:
            frame = np.full_like(frame, 128)  # Gray if no variation
            
        return np.clip(frame, 0, 255).astype(np.uint8)
    
    def extract_lip_region(self, frame):
        """Extract precise lip region using MediaPipe landmarks."""
        h, w, _ = frame.shape

        if HAS_MEDIAPIPE and mp_face_mesh:
            try:
                rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                results = mp_face_mesh.process(rgb)

                if results.multi_face_landmarks:
                    landmarks = results.multi_face_landmarks[0].landmark

                    # MediaPipe lip landmark indices
                    # Outer lip landmarks: 61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318
                    # Inner lip landmarks: 78, 95, 88, 178, 87, 14, 317, 402, 318, 324
                    outer_lip_indices = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318]
                    inner_lip_indices = [78, 95, 88, 178, 87, 14, 317, 402, 318, 324]

                    # Get lip coordinates
                    lip_points = []
                    for idx in outer_lip_indices:
                        x = int(landmarks[idx].x * w)
                        y = int(landmarks[idx].y * h)
                        lip_points.append((x, y))

                    if lip_points:
                        # Calculate bounding box around lips
                        xs = [p[0] for p in lip_points]
                        ys = [p[1] for p in lip_points]

                        min_x, max_x = min(xs), max(xs)
                        min_y, max_y = min(ys), max(ys)

                        # Add padding around lips (20% on each side)
                        lip_width = max_x - min_x
                        lip_height = max_y - min_y

                        padding_x = int(lip_width * 0.3)
                        padding_y = int(lip_height * 0.4)

                        left = max(0, min_x - padding_x)
                        right = min(w, max_x + padding_x)
                        top = max(0, min_y - padding_y)
                        bottom = min(h, max_y + padding_y)

                        # Ensure minimum size
                        if (right - left) < 40 or (bottom - top) < 30:
                            # Fallback to larger region if lips too small
                            center_x, center_y = (min_x + max_x) // 2, (min_y + max_y) // 2
                            left = max(0, center_x - 40)
                            right = min(w, center_x + 40)
                            top = max(0, center_y - 30)
                            bottom = min(h, center_y + 30)

                        crop = frame[top:bottom, left:right]
                        if crop.size > 0:
                            crop_gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
                            return cv2.resize(crop_gray, TARGET_SIZE)

            except Exception as e:
                logger.debug(f"MediaPipe lip detection failed: {e}")

        # Color-based lip detection fallback
        return self.color_based_lip_detection(frame)

    def color_based_lip_detection(self, frame):
        """Fallback lip detection using color analysis."""
        h, w, _ = frame.shape

        # Convert to different color spaces for lip detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        lab = cv2.cvtColor(frame, cv2.COLOR_BGR2LAB)

        # Focus on lower half of face first
        lower_half = frame[h//2:, :]
        hsv_lower = hsv[h//2:, :]
        lab_lower = lab[h//2:, :]

        # Red/pink lip detection in HSV
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 50, 50])
        upper_red2 = np.array([180, 255, 255])

        mask1 = cv2.inRange(hsv_lower, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv_lower, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(mask1, mask2)

        # Find contours in red regions
        contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        if contours:
            # Find largest contour (likely lips)
            largest_contour = max(contours, key=cv2.contourArea)

            if cv2.contourArea(largest_contour) > 100:  # Minimum area threshold
                x, y, w_cont, h_cont = cv2.boundingRect(largest_contour)

                # Adjust coordinates back to full frame
                y += h // 2

                # Add padding
                padding_x = int(w_cont * 0.2)
                padding_y = int(h_cont * 0.3)

                left = max(0, x - padding_x)
                right = min(w, x + w_cont + padding_x)
                top = max(0, y - padding_y)
                bottom = min(h, y + h_cont + padding_y)

                crop = frame[top:bottom, left:right]
                if crop.size > 0:
                    crop_gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
                    return cv2.resize(crop_gray, TARGET_SIZE)

        # Final geometric fallback - center of lower face
        center_x, center_y = w // 2, int(h * 0.75)
        left = max(0, center_x - 60)
        right = min(w, center_x + 60)
        top = max(0, center_y - 40)
        bottom = min(h, center_y + 40)

        crop = frame[top:bottom, left:right]
        crop_gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
        return cv2.resize(crop_gray, TARGET_SIZE)
    
    def sample_frames(self, cap, total_frames):
        """Sample evenly distributed frames from video."""
        if total_frames < TARGET_FRAMES:
            logger.warning(f"Video has only {total_frames} frames, need {TARGET_FRAMES}")
            return []
        
        # Sample frames evenly across the video
        indices = np.linspace(0, total_frames - 1, TARGET_FRAMES).astype(int)
        frames = []
        
        for i in indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, i)
            ret, frame = cap.read()
            if ret and frame is not None:
                frames.append(frame)
            else:
                logger.debug(f"Failed to read frame {i}")
        
        return frames
    
    def process_video(self, input_path, output_path):
        """Process a single video file."""
        cap = cv2.VideoCapture(input_path)
        if not cap.isOpened():
            logger.error(f"Cannot open video: {input_path}")
            self.failed_count += 1
            return False
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames < MIN_FRAMES:
            logger.warning(f"Skipped {os.path.basename(input_path)}: only {total_frames} frames")
            cap.release()
            self.failed_count += 1
            return False
        
        # Sample frames
        frames = self.sample_frames(cap, total_frames)
        cap.release()
        
        if len(frames) < TARGET_FRAMES:
            logger.warning(f"Could only extract {len(frames)} frames from {os.path.basename(input_path)}")
            self.failed_count += 1
            return False
        
        # Process each frame
        processed_frames = []
        for frame in frames:
            try:
                roi = self.extract_lip_region(frame)
                roi = self.enhance_contrast(roi)
                roi = self.normalise_frame(roi)
                processed_frames.append(roi)
            except Exception as e:
                logger.error(f"Error processing frame: {e}")
                self.failed_count += 1
                return False
        
        # Save output video
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)
        
        for frame in processed_frames:
            out.write(frame)
        out.release()
        
        # Verify output
        if os.path.exists(output_path) and os.path.getsize(output_path) > 500:
            logger.info(f"✅ Processed: {os.path.basename(output_path)}")
            self.processed_count += 1
            return True
        else:
            logger.error(f"⚠️ Failed output: {os.path.basename(input_path)}")
            self.failed_count += 1
            return False
    
    def process_directory(self, input_dir, output_dir):
        """Process all videos in input directory."""
        # Find all video files
        video_extensions = ("*.mp4", "*.mov", "*.MOV", "*.mpg", "*.mpeg", "*.MPEG")
        videos = []
        
        for ext in video_extensions:
            videos.extend(glob(os.path.join(input_dir, "**", ext), recursive=True))
        
        logger.info(f"Found {len(videos)} videos to process")
        
        if not videos:
            logger.warning("No video files found!")
            return
        
        # Process each video
        for video_path in tqdm(videos, desc="Processing videos"):
            # Calculate relative path and output path
            rel_path = os.path.relpath(video_path, input_dir)
            output_path = os.path.join(output_dir, rel_path)
            
            # Change extension to .mp4
            output_path = os.path.splitext(output_path)[0] + ".mp4"
            
            self.process_video(video_path, output_path)
        
        # Print summary
        total = self.processed_count + self.failed_count
        success_rate = (self.processed_count / total * 100) if total > 0 else 0
        
        logger.info(f"\n=== Processing Complete ===")
        logger.info(f"✅ Successful: {self.processed_count}")
        logger.info(f"❌ Failed: {self.failed_count}")
        logger.info(f"📊 Success Rate: {success_rate:.1f}%")

def main():
    parser = argparse.ArgumentParser(description="Process real ICU speaker videos for lipreading")
    parser.add_argument('--input', required=True, help='Input directory containing videos')
    parser.add_argument('--output', required=True, help='Output directory for processed videos')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # Validate input directory
    if not os.path.exists(args.input):
        logger.error(f"Input directory does not exist: {args.input}")
        return
    
    # Create output directory
    os.makedirs(args.output, exist_ok=True)
    
    # Process videos
    processor = RealVideoProcessor()
    processor.process_directory(args.input, args.output)

if __name__ == "__main__":
    main()
