#!/usr/bin/env python3
"""
Simple Reliable Lip Cropping
----------------------------
Back to basics - reliable lip detection and cropping using MediaPipe
with proper error handling and visual debugging.
"""

import os
import cv2
import numpy as np
import argparse
from glob import glob

# Try to import tqdm, fallback if not available
try:
    from tqdm import tqdm
except ImportError:
    def tqdm(iterable, desc="Processing"):
        print(f"{desc}...")
        return iterable

# Try to import MediaPipe
try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
    print("✅ MediaPipe available")
except ImportError:
    HAS_MEDIAPIPE = False
    print("❌ MediaPipe not available - using OpenCV face detection")

# Load OpenCV face detector as backup
try:
    face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
    HAS_OPENCV_FACE = True
    print("✅ OpenCV face detection available")
except:
    HAS_OPENCV_FACE = False
    print("❌ OpenCV face detection not available")

# Configuration
TARGET_SIZE = (96, 64)  # width, height
TARGET_FRAMES = 24
TARGET_FPS = 15

class SimpleLipCropper:
    def __init__(self):
        self.processed_count = 0
        self.failed_count = 0
        
        if HAS_MEDIAPIPE:
            self.face_mesh = mp.solutions.face_mesh.FaceMesh(
                static_image_mode=False,
                refine_landmarks=True,
                max_num_faces=1,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
        else:
            self.face_mesh = None
    
    def get_lip_landmarks(self, frame):
        """Get lip landmarks using MediaPipe."""
        if not HAS_MEDIAPIPE or self.face_mesh is None:
            return None
        
        try:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_frame)
            
            if results.multi_face_landmarks:
                landmarks = results.multi_face_landmarks[0]
                h, w, _ = frame.shape
                
                # Get outer lip landmarks (more reliable than inner)
                # These are the key outer lip points in MediaPipe
                outer_lip_indices = [
                    61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318
                ]
                
                lip_points = []
                for idx in outer_lip_indices:
                    x = int(landmarks.landmark[idx].x * w)
                    y = int(landmarks.landmark[idx].y * h)
                    lip_points.append((x, y))
                
                return lip_points
        except Exception as e:
            print(f"MediaPipe error: {e}")
            return None
        
        return None
    
    def crop_lips_from_landmarks(self, frame, lip_points):
        """Crop lips based on landmark points."""
        if not lip_points:
            return None
        
        # Get bounding box of lip points
        xs = [p[0] for p in lip_points]
        ys = [p[1] for p in lip_points]
        
        min_x, max_x = min(xs), max(xs)
        min_y, max_y = min(ys), max(ys)
        
        # Add generous padding around lips
        lip_width = max_x - min_x
        lip_height = max_y - min_y
        
        # Ensure minimum size
        if lip_width < 20 or lip_height < 10:
            return None
        
        # Add 50% padding on each side
        pad_x = int(lip_width * 0.5)
        pad_y = int(lip_height * 0.6)
        
        h, w, _ = frame.shape
        x1 = max(0, min_x - pad_x)
        y1 = max(0, min_y - pad_y)
        x2 = min(w, max_x + pad_x)
        y2 = min(h, max_y + pad_y)
        
        # Crop the region
        crop = frame[y1:y2, x1:x2]
        
        if crop.size == 0:
            return None
        
        return crop
    
    def opencv_face_detection(self, frame):
        """Use OpenCV face detection to find mouth region."""
        if not HAS_OPENCV_FACE:
            return None

        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = face_cascade.detectMultiScale(gray, 1.1, 4)

        if len(faces) > 0:
            # Take the largest face
            face = max(faces, key=lambda x: x[2] * x[3])
            x, y, w, h = face

            # Focus on lower third of face for mouth
            mouth_y = y + int(h * 0.6)  # Start 60% down the face
            mouth_h = int(h * 0.4)      # Take bottom 40% of face
            mouth_x = x + int(w * 0.2)  # Center 60% of face width
            mouth_w = int(w * 0.6)

            # Ensure bounds are valid
            frame_h, frame_w, _ = frame.shape
            mouth_x = max(0, mouth_x)
            mouth_y = max(0, mouth_y)
            mouth_w = min(mouth_w, frame_w - mouth_x)
            mouth_h = min(mouth_h, frame_h - mouth_y)

            if mouth_w > 20 and mouth_h > 15:
                crop = frame[mouth_y:mouth_y+mouth_h, mouth_x:mouth_x+mouth_w]
                return crop

        return None

    def fallback_lip_crop(self, frame):
        """Fallback method - crop lower center of frame."""
        h, w, _ = frame.shape

        # Take lower 40% of frame, center 60% width
        y1 = int(h * 0.6)
        y2 = h
        x1 = int(w * 0.2)
        x2 = int(w * 0.8)

        crop = frame[y1:y2, x1:x2]
        return crop
    
    def process_frame(self, frame):
        """Process a single frame to extract lip region."""
        # Try MediaPipe first
        lip_points = self.get_lip_landmarks(frame)
        
        if lip_points:
            crop = self.crop_lips_from_landmarks(frame, lip_points)
            if crop is not None:
                # Convert to grayscale
                gray_crop = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
                # Resize to target size
                resized = cv2.resize(gray_crop, TARGET_SIZE)
                # Apply histogram equalization
                equalized = cv2.equalizeHist(resized)
                return equalized
        
        # Fallback method
        crop = self.fallback_lip_crop(frame)
        gray_crop = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
        resized = cv2.resize(gray_crop, TARGET_SIZE)
        equalized = cv2.equalizeHist(resized)
        return equalized
    
    def extract_frames(self, video_path):
        """Extract evenly spaced frames from video."""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames < TARGET_FRAMES:
            cap.release()
            return []
        
        # Calculate frame indices
        frame_indices = np.linspace(0, total_frames - 1, TARGET_FRAMES, dtype=int)
        frames = []
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret and frame is not None:
                frames.append(frame)
        
        cap.release()
        return frames
    
    def process_video(self, input_path, output_path):
        """Process a complete video."""
        print(f"Processing: {os.path.basename(input_path)}")
        
        # Extract frames
        frames = self.extract_frames(input_path)
        if len(frames) < TARGET_FRAMES:
            print(f"❌ Could not extract enough frames from {os.path.basename(input_path)}")
            self.failed_count += 1
            return False
        
        # Process each frame
        processed_frames = []
        for frame in frames:
            processed_frame = self.process_frame(frame)
            processed_frames.append(processed_frame)
        
        # Save output video
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)
        
        for frame in processed_frames:
            out.write(frame)
        
        out.release()
        
        # Verify output
        if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
            print(f"✅ Success: {os.path.basename(output_path)}")
            self.processed_count += 1
            return True
        else:
            print(f"❌ Failed: {os.path.basename(input_path)}")
            self.failed_count += 1
            return False
    
    def process_directory(self, input_dir, output_dir):
        """Process all videos in directory."""
        # Find all MP4 files
        video_files = glob(os.path.join(input_dir, "*.mp4"))
        
        if not video_files:
            print("No MP4 files found!")
            return
        
        print(f"Found {len(video_files)} videos to process")
        
        for video_path in tqdm(video_files, desc="Processing videos"):
            filename = os.path.basename(video_path)
            output_path = os.path.join(output_dir, filename)
            self.process_video(video_path, output_path)
        
        # Print summary
        total = self.processed_count + self.failed_count
        success_rate = (self.processed_count / total * 100) if total > 0 else 0
        
        print(f"\n=== Processing Complete ===")
        print(f"✅ Successful: {self.processed_count}")
        print(f"❌ Failed: {self.failed_count}")
        print(f"📊 Success Rate: {success_rate:.1f}%")

def main():
    parser = argparse.ArgumentParser(description="Simple reliable lip cropping")
    parser.add_argument('--input', required=True, help='Input directory with MP4 files')
    parser.add_argument('--output', required=True, help='Output directory')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"Input directory does not exist: {args.input}")
        return
    
    os.makedirs(args.output, exist_ok=True)
    
    cropper = SimpleLipCropper()
    cropper.process_directory(args.input, args.output)

if __name__ == "__main__":
    main()
