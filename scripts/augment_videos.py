"""
Video Augmentation Script for ICU Lipreading Project
Applies controlled augmentations following SRAVI pipeline
"""

import numpy as np
import cv2
import argparse
from pathlib import Path
from typing import List, Tuple, Optional
import json
import random
import sys

# Add utils to path
sys.path.append(str(Path(__file__).parent.parent))
from utils.config import get_config

class VideoAugmenter:
    """Applies augmentations to video sequences"""
    
    def __init__(self, config_dict: dict):
        self.scale_range = config_dict.get('scale_range', [0.9, 1.1])
        self.shift_range = config_dict.get('shift_range', 0.1)
        self.brightness_range = config_dict.get('brightness_range', [0.8, 1.2])
        self.contrast_range = config_dict.get('contrast_range', [0.8, 1.2])
        self.gamma_range = config_dict.get('gamma_range', [0.8, 1.2])
        self.blur_kernel_size = config_dict.get('blur_kernel_size', 3)
        self.temporal_crop = config_dict.get('temporal_crop', True)
        self.mixup_alpha = config_dict.get('mixup_alpha', 0.2)
    
    def random_scale_shift(self, frames: np.ndarray) -> np.ndarray:
        """Apply random scaling and shifting"""
        h, w = frames.shape[1], frames.shape[2]
        
        # Random scale
        scale = np.random.uniform(self.scale_range[0], self.scale_range[1])
        
        # Random shift
        shift_x = np.random.uniform(-self.shift_range, self.shift_range) * w
        shift_y = np.random.uniform(-self.shift_range, self.shift_range) * h
        
        # Create transformation matrix
        M = np.float32([
            [scale, 0, shift_x],
            [0, scale, shift_y]
        ])
        
        augmented_frames = []
        for frame in frames:
            # Convert to uint8 for transformation
            frame_uint8 = np.clip((frame + 1) * 127.5, 0, 255).astype(np.uint8)
            
            # Apply transformation
            transformed = cv2.warpAffine(frame_uint8, M, (w, h), borderMode=cv2.BORDER_REFLECT)
            
            # Convert back to normalized float
            transformed = (transformed.astype(np.float32) / 127.5) - 1
            augmented_frames.append(transformed)
        
        return np.array(augmented_frames)
    
    def random_brightness_contrast(self, frames: np.ndarray) -> np.ndarray:
        """Apply random brightness and contrast changes"""
        brightness = np.random.uniform(self.brightness_range[0], self.brightness_range[1])
        contrast = np.random.uniform(self.contrast_range[0], self.contrast_range[1])
        
        # Apply brightness and contrast
        augmented = frames * contrast + (brightness - 1)
        
        # Clip to valid range
        augmented = np.clip(augmented, -1, 1)
        
        return augmented
    
    def random_gamma(self, frames: np.ndarray) -> np.ndarray:
        """Apply random gamma correction"""
        gamma = np.random.uniform(self.gamma_range[0], self.gamma_range[1])
        
        # Convert to [0, 1] range for gamma correction
        frames_01 = (frames + 1) / 2
        
        # Apply gamma correction
        augmented = np.power(frames_01, gamma)
        
        # Convert back to [-1, 1] range
        augmented = augmented * 2 - 1
        
        return augmented
    
    def random_blur(self, frames: np.ndarray) -> np.ndarray:
        """Apply random Gaussian blur"""
        if np.random.random() < 0.3:  # 30% chance of blur
            augmented_frames = []
            
            for frame in frames:
                # Convert to uint8 for blurring
                frame_uint8 = np.clip((frame + 1) * 127.5, 0, 255).astype(np.uint8)
                
                # Apply Gaussian blur
                blurred = cv2.GaussianBlur(frame_uint8, (self.blur_kernel_size, self.blur_kernel_size), 0)
                
                # Convert back to normalized float
                blurred = (blurred.astype(np.float32) / 127.5) - 1
                augmented_frames.append(blurred)
            
            return np.array(augmented_frames)
        
        return frames
    
    def temporal_crop_augment(self, frames: np.ndarray, crop_ratio: float = 0.8) -> np.ndarray:
        """Apply temporal cropping and padding"""
        if not self.temporal_crop or np.random.random() < 0.5:
            return frames
        
        num_frames = len(frames)
        crop_length = int(num_frames * crop_ratio)
        
        if crop_length < num_frames:
            # Random start position
            start_idx = np.random.randint(0, num_frames - crop_length + 1)
            cropped = frames[start_idx:start_idx + crop_length]
            
            # Pad back to original length
            padding_needed = num_frames - crop_length
            pad_start = padding_needed // 2
            pad_end = padding_needed - pad_start
            
            padded_frames = []
            
            # Pad start
            for _ in range(pad_start):
                padded_frames.append(cropped[0])
            
            # Add cropped frames
            padded_frames.extend(cropped)
            
            # Pad end
            for _ in range(pad_end):
                padded_frames.append(cropped[-1])
            
            return np.array(padded_frames)
        
        return frames
    
    def mixup(self, frames1: np.ndarray, frames2: np.ndarray, alpha: Optional[float] = None) -> Tuple[np.ndarray, float]:
        """Apply mixup augmentation between two video sequences"""
        if alpha is None:
            alpha = self.mixup_alpha
        
        if alpha <= 0:
            return frames1, 1.0
        
        # Sample mixing coefficient
        lam = np.random.beta(alpha, alpha)
        
        # Mix frames
        mixed_frames = lam * frames1 + (1 - lam) * frames2
        
        return mixed_frames, lam
    
    def augment_sequence(self, frames: np.ndarray, apply_all: bool = False) -> np.ndarray:
        """Apply random augmentations to a video sequence"""
        augmented = frames.copy()
        
        # Spatial augmentations
        if apply_all or np.random.random() < 0.7:
            augmented = self.random_scale_shift(augmented)
        
        if apply_all or np.random.random() < 0.6:
            augmented = self.random_brightness_contrast(augmented)
        
        if apply_all or np.random.random() < 0.5:
            augmented = self.random_gamma(augmented)
        
        if apply_all or np.random.random() < 0.3:
            augmented = self.random_blur(augmented)
        
        # Temporal augmentations
        if apply_all or np.random.random() < 0.4:
            augmented = self.temporal_crop_augment(augmented)
        
        return augmented
    
    def augment_dataset(self, input_dir: Path, output_dir: Path, manifest_path: Path, 
                       augmentation_factor: int = 2) -> None:
        """Augment entire dataset"""
        
        # Load original manifest
        with open(manifest_path, 'r') as f:
            original_manifest = json.load(f)
        
        augmented_manifest = []
        
        print(f"🔄 Augmenting dataset with factor {augmentation_factor}")
        
        for item in original_manifest:
            # Add original item
            augmented_manifest.append(item)
            
            # Load original video
            video_path = Path(item['video_path'])
            if not video_path.exists():
                print(f"⚠️  Video not found: {video_path}")
                continue
            
            frames = np.load(video_path)
            
            # Generate augmented versions
            for aug_idx in range(augmentation_factor):
                augmented_frames = self.augment_sequence(frames)
                
                # Save augmented video
                aug_filename = f"{video_path.stem}_aug{aug_idx}{video_path.suffix}"
                aug_path = output_dir / video_path.parent.name / aug_filename
                aug_path.parent.mkdir(parents=True, exist_ok=True)
                
                np.save(aug_path, augmented_frames)
                
                # Add to manifest
                aug_item = item.copy()
                aug_item['video_path'] = str(aug_path)
                aug_item['augmented'] = True
                aug_item['augmentation_id'] = aug_idx
                augmented_manifest.append(aug_item)
        
        # Save augmented manifest
        aug_manifest_path = output_dir / "augmented_manifest.json"
        with open(aug_manifest_path, 'w') as f:
            json.dump(augmented_manifest, f, indent=2)
        
        print(f"✅ Created {len(augmented_manifest)} augmented samples")
        print(f"📋 Saved augmented manifest: {aug_manifest_path}")

def main():
    parser = argparse.ArgumentParser(description="Augment video dataset")
    parser.add_argument("--input", "-i", type=str, required=True, help="Input directory with processed videos")
    parser.add_argument("--output", "-o", type=str, required=True, help="Output directory for augmented videos")
    parser.add_argument("--manifest", "-m", type=str, required=True, help="Input manifest file")
    parser.add_argument("--factor", "-f", type=int, default=2, help="Augmentation factor")
    parser.add_argument("--config", "-c", type=str, help="Custom config file")
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config:
        import yaml
        with open(args.config, 'r') as f:
            config_dict = yaml.safe_load(f)['augmentation']
    else:
        config = get_config()
        config_dict = config.get_augmentation_config()
    
    augmenter = VideoAugmenter(config_dict)
    
    input_dir = Path(args.input)
    output_dir = Path(args.output)
    manifest_path = Path(args.manifest)
    
    if not input_dir.exists():
        print(f"❌ Input directory not found: {input_dir}")
        return
    
    if not manifest_path.exists():
        print(f"❌ Manifest file not found: {manifest_path}")
        return
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # Augment dataset
    augmenter.augment_dataset(input_dir, output_dir, manifest_path, args.factor)

if __name__ == "__main__":
    main()
