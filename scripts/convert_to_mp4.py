#!/usr/bin/env python3
"""
Convert All Videos to MP4
-------------------------
Converts all video files in a directory to standardized MP4 format
using FFmpeg for consistent codec and container format.
"""

import os
import subprocess
import argparse
from glob import glob
from pathlib import Path

# Try to import tqdm, fallback to basic iteration if not available
try:
    from tqdm import tqdm
except ImportError:
    def tqdm(iterable, desc="Processing"):
        print(f"{desc}...")
        return iterable

def convert_video_to_mp4(input_path, output_path):
    """Convert a single video file to MP4 format."""
    try:
        # FFmpeg command for high-quality MP4 conversion
        cmd = [
            'ffmpeg',
            '-y',  # Overwrite output files
            '-hide_banner',  # Hide FFmpeg banner
            '-loglevel', 'error',  # Only show errors
            '-i', input_path,  # Input file
            '-c:v', 'libx264',  # H.264 video codec
            '-preset', 'medium',  # Encoding speed/quality balance
            '-crf', '18',  # High quality (lower = better quality)
            '-c:a', 'aac',  # AAC audio codec (if audio exists)
            '-b:a', '128k',  # Audio bitrate
            '-movflags', '+faststart',  # Optimize for web streaming
            '-pix_fmt', 'yuv420p',  # Ensure compatibility
            output_path
        ]
        
        # Run FFmpeg conversion
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            # Verify output file was created and has reasonable size
            if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                return True, "Success"
            else:
                return False, "Output file too small or missing"
        else:
            return False, f"FFmpeg error: {result.stderr}"
            
    except Exception as e:
        return False, f"Exception: {str(e)}"

def convert_directory_to_mp4(input_dir, output_dir=None):
    """Convert all videos in a directory to MP4 format."""
    
    # If no output directory specified, create one
    if output_dir is None:
        output_dir = os.path.join(os.path.dirname(input_dir), f"{os.path.basename(input_dir)}_mp4")
    
    os.makedirs(output_dir, exist_ok=True)
    
    # Find all video files
    video_extensions = ["*.mov", "*.MOV", "*.mpg", "*.mpeg", "*.MPEG", "*.avi", "*.AVI", "*.mkv", "*.MKV"]
    video_files = []
    
    for ext in video_extensions:
        video_files.extend(glob(os.path.join(input_dir, "**", ext), recursive=True))
    
    if not video_files:
        print("No video files found to convert!")
        return
    
    print(f"Found {len(video_files)} videos to convert to MP4")
    
    successful = 0
    failed = 0
    
    for video_path in tqdm(video_files, desc="Converting videos"):
        # Get relative path and create output path
        rel_path = os.path.relpath(video_path, input_dir)
        output_path = os.path.join(output_dir, rel_path)
        
        # Change extension to .mp4
        output_path = os.path.splitext(output_path)[0] + ".mp4"
        
        # Create output directory if needed
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # Skip if already exists and is newer
        if os.path.exists(output_path):
            if os.path.getmtime(output_path) > os.path.getmtime(video_path):
                print(f"⏭️  Skipped (already exists): {os.path.basename(output_path)}")
                successful += 1
                continue
        
        # Convert video
        success, message = convert_video_to_mp4(video_path, output_path)
        
        if success:
            print(f"✅ Converted: {os.path.basename(video_path)} -> {os.path.basename(output_path)}")
            successful += 1
        else:
            print(f"❌ Failed: {os.path.basename(video_path)} - {message}")
            failed += 1
    
    print(f"\n=== Conversion Complete ===")
    print(f"✅ Successful: {successful}")
    print(f"❌ Failed: {failed}")
    print(f"📊 Success Rate: {successful/(successful+failed)*100:.1f}%")
    print(f"📁 Output directory: {output_dir}")

def main():
    parser = argparse.ArgumentParser(description="Convert all videos in a directory to MP4 format")
    parser.add_argument('--input', help='Input directory containing videos')
    parser.add_argument('--output', help='Output directory for MP4 files (optional)')
    parser.add_argument('--check-ffmpeg', action='store_true', help='Check if FFmpeg is available')

    args = parser.parse_args()

    # Check if FFmpeg is available
    if args.check_ffmpeg:
        try:
            result = subprocess.run(['ffmpeg', '-version'], capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ FFmpeg is available")
                print(f"Version: {result.stdout.split()[2]}")
            else:
                print("❌ FFmpeg not found")
        except FileNotFoundError:
            print("❌ FFmpeg not found. Please install FFmpeg first.")
            print("Install with: brew install ffmpeg (on macOS)")
        return

    # Require input if not just checking FFmpeg
    if not args.input:
        parser.error("--input is required unless using --check-ffmpeg")
    
    # Validate input directory
    if not os.path.exists(args.input):
        print(f"Error: Input directory does not exist: {args.input}")
        return
    
    # Check FFmpeg availability
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
    except (FileNotFoundError, subprocess.CalledProcessError):
        print("❌ FFmpeg not found. Please install FFmpeg first.")
        print("Install with: brew install ffmpeg (on macOS)")
        return
    
    # Convert videos
    convert_directory_to_mp4(args.input, args.output)

if __name__ == "__main__":
    main()
