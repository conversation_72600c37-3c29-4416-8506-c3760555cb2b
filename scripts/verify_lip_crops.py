#!/usr/bin/env python3
"""
Verify Lip Crops - Quick visual verification
"""

import cv2
import os
import numpy as np
from glob import glob

def verify_crops():
    input_dir = "/Users/<USER>/Desktop/LRP final/data/processed/vertical_adaptive_lips"
    
    # Get a few sample videos
    videos = glob(os.path.join(input_dir, "*.mp4"))[:5]
    
    print(f"Verifying {len(videos)} sample videos...")
    
    for video_path in videos:
        filename = os.path.basename(video_path)
        print(f"\n📹 {filename}")
        
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            print("❌ Cannot open video")
            continue
        
        # Read first frame
        ret, frame = cap.read()
        if ret:
            h, w = frame.shape[:2]
            print(f"   📐 Dimensions: {w}x{h}")
            print(f"   🎯 Target: 96x64")
            
            if w == 96 and h == 64:
                print("   ✅ Correct dimensions")
            else:
                print("   ❌ Wrong dimensions")
        
        # Check total frames
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        fps = cap.get(cv2.CAP_PROP_FPS)
        print(f"   🎬 Frames: {total_frames} @ {fps:.1f}fps")
        
        if total_frames == 24:
            print("   ✅ Correct frame count")
        else:
            print("   ❌ Wrong frame count")
        
        cap.release()
    
    print(f"\n✅ Verification complete!")

if __name__ == "__main__":
    verify_crops()
