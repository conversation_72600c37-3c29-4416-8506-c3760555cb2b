#!/usr/bin/env python3
"""
Fixed MediaPipe Lip Preprocessing with Debug Mode
------------------------------------------------
Addresses the "striped frame" issue by:
1. Forcing consistent resolution before detection
2. Using only outer lip landmarks for stability
3. Skipping frames with failed detection (no fallback reuse)
4. Optional debug mode to visualize detection boxes
"""

import os
import cv2
import numpy as np
import mediapipe as mp
import argparse
from glob import glob

# Try to import tqdm
try:
    from tqdm import tqdm
except ImportError:
    def tqdm(iterable, desc="Processing"):
        print(f"{desc}...")
        return iterable

# Configuration
TARGET_SIZE = (96, 64)
TARGET_FRAMES = 24
TARGET_FPS = 15
STANDARD_RESOLUTION = (480, 360)  # Force all videos to this resolution first

# MediaPipe setup
mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
    static_image_mode=False,
    refine_landmarks=False,  # Keep disabled for stability
    max_num_faces=1,
    min_detection_confidence=0.5,  # Higher threshold
    min_tracking_confidence=0.5
)

# Only outer lip landmarks (more stable than inner+outer)
OUTER_LIP_INDICES = [
    61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318
]

class FixedLipProcessor:
    def __init__(self, debug_mode=False):
        self.debug_mode = debug_mode
        self.processed_count = 0
        self.failed_count = 0
    
    def standardize_frame(self, frame):
        """Force frame to standard resolution to help MediaPipe."""
        h, w = frame.shape[:2]
        if (w, h) != STANDARD_RESOLUTION:
            frame = cv2.resize(frame, STANDARD_RESOLUTION)
        return frame
    
    def detect_lip_landmarks(self, frame):
        """Detect lip landmarks with strict validation."""
        # Ensure frame is in standard resolution
        frame = self.standardize_frame(frame)
        h, w = frame.shape[:2]
        
        # Convert to RGB for MediaPipe
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        try:
            results = mp_face_mesh.process(rgb_frame)
            
            if not results.multi_face_landmarks:
                return None, None
            
            landmarks = results.multi_face_landmarks[0]
            
            # Extract outer lip points
            lip_points = []
            for idx in OUTER_LIP_INDICES:
                lm = landmarks.landmark[idx]
                x = int(lm.x * w)
                y = int(lm.y * h)
                
                # Validate coordinates are within frame bounds
                if 0 <= x < w and 0 <= y < h:
                    lip_points.append((x, y))
            
            # Need at least 10 valid points for reliable detection
            if len(lip_points) < 10:
                return None, None
            
            # Calculate bounding box
            xs = [p[0] for p in lip_points]
            ys = [p[1] for p in lip_points]
            
            min_x, max_x = min(xs), max(xs)
            min_y, max_y = min(ys), max(ys)
            
            # Validate bounding box size
            lip_width = max_x - min_x
            lip_height = max_y - min_y
            
            if lip_width < 15 or lip_height < 8:  # Minimum reasonable lip size
                return None, None
            
            # Add padding
            pad_x = int(lip_width * 0.4)  # Slightly more padding
            pad_y = int(lip_height * 0.5)
            
            x1 = max(0, min_x - pad_x)
            y1 = max(0, min_y - pad_y)
            x2 = min(w, max_x + pad_x)
            y2 = min(h, max_y + pad_y)
            
            # Final validation of crop region
            if x2 <= x1 or y2 <= y1:
                return None, None
            
            return (x1, y1, x2, y2), frame
            
        except Exception as e:
            print(f"MediaPipe error: {e}")
            return None, None
    
    def extract_lip_crop(self, frame, bbox):
        """Extract and process lip crop from frame."""
        x1, y1, x2, y2 = bbox
        
        # Extract crop
        crop = frame[y1:y2, x1:x2]
        
        if crop.size == 0:
            return None
        
        # Convert to grayscale
        gray_crop = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
        
        # Apply CLAHE for contrast enhancement
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray_crop)
        
        # Resize to target size
        resized = cv2.resize(enhanced, TARGET_SIZE)
        
        # Normalize
        normalized = cv2.equalizeHist(resized)
        
        return normalized
    
    def extract_frames_evenly(self, video_path):
        """Extract evenly spaced frames from video."""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames < TARGET_FRAMES:
            cap.release()
            return []
        
        # Calculate frame indices
        frame_indices = np.linspace(0, total_frames - 1, TARGET_FRAMES, dtype=int)
        frames = []
        
        for idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, idx)
            ret, frame = cap.read()
            if ret and frame is not None:
                frames.append(frame)
        
        cap.release()
        return frames
    
    def process_video(self, input_path, output_path, debug_output_path=None):
        """Process a single video."""
        filename = os.path.basename(input_path)
        print(f"Processing: {filename}")
        
        # Extract frames
        frames = self.extract_frames_evenly(input_path)
        if len(frames) < TARGET_FRAMES:
            print(f"❌ Insufficient frames: {filename}")
            self.failed_count += 1
            return False
        
        # Process each frame
        valid_crops = []
        debug_frames = []
        detection_count = 0
        
        for i, frame in enumerate(frames):
            bbox, std_frame = self.detect_lip_landmarks(frame)
            
            if bbox is not None and std_frame is not None:
                # Extract lip crop
                lip_crop = self.extract_lip_crop(std_frame, bbox)
                
                if lip_crop is not None:
                    valid_crops.append(lip_crop)
                    detection_count += 1
                    
                    # Create debug frame if needed
                    if self.debug_mode:
                        debug_frame = std_frame.copy()
                        x1, y1, x2, y2 = bbox
                        cv2.rectangle(debug_frame, (x1, y1), (x2, y2), (0, 255, 0), 2)
                        cv2.putText(debug_frame, f"Frame {i+1}", (10, 30), 
                                  cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
                        debug_frames.append(debug_frame)
                    
                    continue
            
            # No valid detection for this frame - skip it entirely
            print(f"   ⚠️ Skipped frame {i+1}/{len(frames)} (no detection)")
        
        # Check if we have enough valid detections
        detection_rate = (detection_count / len(frames)) * 100
        print(f"   📊 Detection rate: {detection_rate:.1f}% ({detection_count}/{len(frames)})")
        
        if detection_count < TARGET_FRAMES // 2:  # Need at least 50% success
            print(f"❌ Insufficient detections: {filename}")
            self.failed_count += 1
            return False
        
        # Pad with duplicates if needed to reach TARGET_FRAMES
        while len(valid_crops) < TARGET_FRAMES:
            if valid_crops:
                valid_crops.append(valid_crops[-1])  # Duplicate last valid frame
        
        # Trim to exact target frames
        valid_crops = valid_crops[:TARGET_FRAMES]
        
        # Save main output
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)
        
        for crop in valid_crops:
            out.write(crop)
        
        out.release()
        
        # Save debug output if requested
        if self.debug_mode and debug_output_path and debug_frames:
            os.makedirs(os.path.dirname(debug_output_path), exist_ok=True)
            debug_out = cv2.VideoWriter(debug_output_path, fourcc, TARGET_FPS, STANDARD_RESOLUTION)
            
            # Pad debug frames to match
            while len(debug_frames) < len(valid_crops):
                if debug_frames:
                    debug_frames.append(debug_frames[-1])
            
            for debug_frame in debug_frames[:TARGET_FRAMES]:
                debug_out.write(debug_frame)
            
            debug_out.release()
        
        # Verify output
        if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
            print(f"✅ Success: {filename}")
            self.processed_count += 1
            return True
        else:
            print(f"❌ Output failed: {filename}")
            self.failed_count += 1
            return False
    
    def process_directory(self, input_dir, output_dir, debug_dir=None):
        """Process all videos in directory."""
        # Find all MP4 files
        video_files = glob(os.path.join(input_dir, "*.mp4"))
        
        if not video_files:
            print("❌ No MP4 files found!")
            return
        
        print(f"Found {len(video_files)} videos to process")
        if self.debug_mode:
            print("🔍 Debug mode enabled - will save detection visualizations")
        
        for video_path in tqdm(video_files, desc="Processing"):
            filename = os.path.basename(video_path)
            output_path = os.path.join(output_dir, filename)
            
            debug_output_path = None
            if self.debug_mode and debug_dir:
                debug_filename = f"debug_{filename}"
                debug_output_path = os.path.join(debug_dir, debug_filename)
            
            self.process_video(video_path, output_path, debug_output_path)
        
        # Summary
        total = self.processed_count + self.failed_count
        success_rate = (self.processed_count / total * 100) if total > 0 else 0
        
        print(f"\n=== PROCESSING COMPLETE ===")
        print(f"✅ Successful: {self.processed_count}")
        print(f"❌ Failed: {self.failed_count}")
        print(f"📊 Success Rate: {success_rate:.1f}%")
        print(f"📁 Output: {output_dir}")
        if self.debug_mode and debug_dir:
            print(f"🔍 Debug: {debug_dir}")

def main():
    parser = argparse.ArgumentParser(description="Fixed MediaPipe lip preprocessing")
    parser.add_argument("--input", required=True, help="Input directory with MP4 files")
    parser.add_argument("--output", required=True, help="Output directory for processed videos")
    parser.add_argument("--debug", action="store_true", help="Enable debug mode with visualization")
    parser.add_argument("--debug-dir", help="Directory for debug videos (if debug mode enabled)")
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"❌ Input directory does not exist: {args.input}")
        return
    
    os.makedirs(args.output, exist_ok=True)
    
    debug_dir = None
    if args.debug:
        debug_dir = args.debug_dir or os.path.join(os.path.dirname(args.output), "debug_fixed_lips")
        os.makedirs(debug_dir, exist_ok=True)
    
    processor = FixedLipProcessor(debug_mode=args.debug)
    processor.process_directory(args.input, args.output, debug_dir)

if __name__ == "__main__":
    main()
