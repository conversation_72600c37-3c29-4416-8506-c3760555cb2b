#!/usr/bin/env python3
"""
Bottom Half Face Lip Cropping
------------------------------
Specifically designed for videos showing only the bottom half of faces.
Uses MediaPipe and YOLO with adaptations for partial face detection.
"""

import os
import cv2
import numpy as np
import argparse
from glob import glob

# Try to import required libraries
try:
    from tqdm import tqdm
except ImportError:
    def tqdm(iterable, desc="Processing"):
        print(f"{desc}...")
        return iterable

try:
    import mediapipe as mp
    HAS_MEDIAPIPE = True
    print("✅ MediaPipe available")
except ImportError:
    HAS_MEDIAPIPE = False
    print("❌ MediaPipe not available")

try:
    from ultralytics import YOLO
    HAS_YOLO = True
    print("✅ YOLO available")
except ImportError:
    HAS_YOLO = False
    print("❌ YOLO not available")

# Configuration
TARGET_SIZE = (96, 64)  # width, height
TARGET_FRAMES = 24
TARGET_FPS = 15

class BottomHalfLipCropper:
    def __init__(self):
        self.processed_count = 0
        self.failed_count = 0
        
        # Initialize MediaPipe with adjusted settings for partial faces
        if HAS_MEDIAPIPE:
            self.face_mesh = mp.solutions.face_mesh.FaceMesh(
                static_image_mode=False,
                refine_landmarks=True,
                max_num_faces=1,
                min_detection_confidence=0.3,  # Lower threshold for partial faces
                min_tracking_confidence=0.3
            )
        else:
            self.face_mesh = None
        
        # Initialize YOLO for face detection
        if HAS_YOLO:
            try:
                self.yolo_model = YOLO('yolov8n-face.pt')  # Face-specific model
                print("✅ YOLO face model loaded")
            except:
                try:
                    self.yolo_model = YOLO('yolov8n.pt')  # General model
                    print("✅ YOLO general model loaded")
                except:
                    self.yolo_model = None
                    print("❌ YOLO model loading failed")
        else:
            self.yolo_model = None
    
    def detect_lips_mediapipe(self, frame):
        """Detect lips using MediaPipe, adapted for bottom-half faces."""
        if not HAS_MEDIAPIPE or self.face_mesh is None:
            return None
        
        try:
            # Since we have bottom half, we might need to pad the top to help MediaPipe
            h, w, _ = frame.shape
            
            # Create a padded frame (add black top half)
            padded_frame = np.zeros((h * 2, w, 3), dtype=np.uint8)
            padded_frame[h:, :] = frame  # Put original frame in bottom half
            
            rgb_frame = cv2.cvtColor(padded_frame, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_frame)
            
            if results.multi_face_landmarks:
                landmarks = results.multi_face_landmarks[0]
                padded_h, padded_w, _ = padded_frame.shape
                
                # Key lip landmark indices for MediaPipe
                outer_lip_indices = [
                    61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318
                ]
                
                lip_points = []
                for idx in outer_lip_indices:
                    x = int(landmarks.landmark[idx].x * padded_w)
                    y = int(landmarks.landmark[idx].y * padded_h)
                    
                    # Adjust coordinates back to original frame
                    if y >= h:  # Only keep points in the bottom half
                        lip_points.append((x, y - h))
                
                if len(lip_points) >= 4:  # Need at least 4 points for a bounding box
                    return lip_points
                    
        except Exception as e:
            print(f"MediaPipe error: {e}")
        
        return None
    
    def detect_mouth_yolo(self, frame):
        """Use YOLO to detect face region, then focus on mouth area."""
        if not HAS_YOLO or self.yolo_model is None:
            return None
        
        try:
            results = self.yolo_model(frame, verbose=False)
            
            if results and len(results[0].boxes) > 0:
                # Get the largest detection (most confident face)
                boxes = results[0].boxes
                confidences = boxes.conf.cpu().numpy()
                best_idx = np.argmax(confidences)
                
                if confidences[best_idx] > 0.3:  # Confidence threshold
                    box = boxes.xyxy[best_idx].cpu().numpy()
                    x1, y1, x2, y2 = map(int, box)
                    
                    # Since this is bottom half of face, the mouth should be in lower portion
                    face_h = y2 - y1
                    face_w = x2 - x1
                    
                    # Focus on lower 60% of detected face region for mouth
                    mouth_y1 = y1 + int(face_h * 0.4)
                    mouth_y2 = y2
                    mouth_x1 = x1 + int(face_w * 0.15)  # Slight margin from sides
                    mouth_x2 = x2 - int(face_w * 0.15)
                    
                    return [mouth_x1, mouth_y1, mouth_x2, mouth_y2]
                    
        except Exception as e:
            print(f"YOLO error: {e}")
        
        return None
    
    def color_based_lip_detection(self, frame):
        """Detect lips using color analysis - works well for bottom-half faces."""
        h, w, _ = frame.shape
        
        # Convert to HSV for better color detection
        hsv = cv2.cvtColor(frame, cv2.COLOR_BGR2HSV)
        
        # Define red/pink ranges for lips
        lower_red1 = np.array([0, 50, 50])
        upper_red1 = np.array([10, 255, 255])
        lower_red2 = np.array([160, 50, 50])
        upper_red2 = np.array([180, 255, 255])
        
        # Create masks for red regions
        mask1 = cv2.inRange(hsv, lower_red1, upper_red1)
        mask2 = cv2.inRange(hsv, lower_red2, upper_red2)
        red_mask = cv2.bitwise_or(mask1, mask2)
        
        # Focus on center region where mouth is likely to be
        center_mask = np.zeros_like(red_mask)
        center_y1 = int(h * 0.3)
        center_y2 = int(h * 0.8)
        center_x1 = int(w * 0.2)
        center_x2 = int(w * 0.8)
        center_mask[center_y1:center_y2, center_x1:center_x2] = 255
        
        # Combine red detection with center focus
        combined_mask = cv2.bitwise_and(red_mask, center_mask)
        
        # Find contours
        contours, _ = cv2.findContours(combined_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        if contours:
            # Find the largest contour
            largest_contour = max(contours, key=cv2.contourArea)
            
            if cv2.contourArea(largest_contour) > 50:  # Minimum area
                x, y, w_cont, h_cont = cv2.boundingRect(largest_contour)
                
                # Add padding
                pad_x = int(w_cont * 0.3)
                pad_y = int(h_cont * 0.4)
                
                x1 = max(0, x - pad_x)
                y1 = max(0, y - pad_y)
                x2 = min(w, x + w_cont + pad_x)
                y2 = min(h, y + h_cont + pad_y)
                
                return [x1, y1, x2, y2]
        
        return None
    
    def extract_lip_region(self, frame):
        """Main lip extraction function with multiple detection methods."""
        
        # Method 1: Try MediaPipe first
        lip_points = self.detect_lips_mediapipe(frame)
        if lip_points:
            xs = [p[0] for p in lip_points]
            ys = [p[1] for p in lip_points]
            
            min_x, max_x = min(xs), max(xs)
            min_y, max_y = min(ys), max(ys)
            
            # Add padding
            lip_w = max_x - min_x
            lip_h = max_y - min_y
            
            if lip_w > 15 and lip_h > 8:  # Minimum size check
                pad_x = int(lip_w * 0.4)
                pad_y = int(lip_h * 0.5)
                
                h, w, _ = frame.shape
                x1 = max(0, min_x - pad_x)
                y1 = max(0, min_y - pad_y)
                x2 = min(w, max_x + pad_x)
                y2 = min(h, max_y + pad_y)
                
                crop = frame[y1:y2, x1:x2]
                if crop.size > 0:
                    return crop
        
        # Method 2: Try YOLO detection
        yolo_box = self.detect_mouth_yolo(frame)
        if yolo_box:
            x1, y1, x2, y2 = yolo_box
            crop = frame[y1:y2, x1:x2]
            if crop.size > 0:
                return crop
        
        # Method 3: Color-based detection
        color_box = self.color_based_lip_detection(frame)
        if color_box:
            x1, y1, x2, y2 = color_box
            crop = frame[y1:y2, x1:x2]
            if crop.size > 0:
                return crop
        
        # Method 4: Geometric fallback for bottom-half faces
        h, w, _ = frame.shape
        # Focus on center region of bottom-half face
        y1 = int(h * 0.4)  # Start 40% down
        y2 = int(h * 0.85) # End at 85%
        x1 = int(w * 0.25) # Center 50% width
        x2 = int(w * 0.75)
        
        crop = frame[y1:y2, x1:x2]
        return crop
    
    def process_frame(self, frame):
        """Process a single frame."""
        # Extract lip region
        lip_crop = self.extract_lip_region(frame)
        
        if lip_crop is None or lip_crop.size == 0:
            return None
        
        # Convert to grayscale
        gray_crop = cv2.cvtColor(lip_crop, cv2.COLOR_BGR2GRAY)
        
        # Resize to target size
        resized = cv2.resize(gray_crop, TARGET_SIZE)
        
        # Apply histogram equalization for better contrast
        equalized = cv2.equalizeHist(resized)
        
        return equalized
    
    def extract_frames(self, video_path):
        """Extract evenly spaced frames from video."""
        cap = cv2.VideoCapture(video_path)
        if not cap.isOpened():
            return []
        
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        if total_frames < TARGET_FRAMES:
            cap.release()
            return []
        
        # Calculate frame indices
        frame_indices = np.linspace(0, total_frames - 1, TARGET_FRAMES, dtype=int)
        frames = []
        
        for frame_idx in frame_indices:
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_idx)
            ret, frame = cap.read()
            if ret and frame is not None:
                frames.append(frame)
        
        cap.release()
        return frames
    
    def process_video(self, input_path, output_path):
        """Process a complete video."""
        print(f"Processing: {os.path.basename(input_path)}")
        
        # Extract frames
        frames = self.extract_frames(input_path)
        if len(frames) < TARGET_FRAMES:
            print(f"❌ Could not extract enough frames from {os.path.basename(input_path)}")
            self.failed_count += 1
            return False
        
        # Process each frame
        processed_frames = []
        for frame in frames:
            processed_frame = self.process_frame(frame)
            if processed_frame is not None:
                processed_frames.append(processed_frame)
        
        if len(processed_frames) < TARGET_FRAMES:
            print(f"❌ Could not process enough frames from {os.path.basename(input_path)}")
            self.failed_count += 1
            return False
        
        # Save output video
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(output_path, fourcc, TARGET_FPS, TARGET_SIZE, isColor=False)
        
        for frame in processed_frames:
            out.write(frame)
        
        out.release()
        
        # Verify output
        if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
            print(f"✅ Success: {os.path.basename(output_path)}")
            self.processed_count += 1
            return True
        else:
            print(f"❌ Failed: {os.path.basename(input_path)}")
            self.failed_count += 1
            return False
    
    def process_directory(self, input_dir, output_dir):
        """Process all videos in directory."""
        video_files = glob(os.path.join(input_dir, "*.mp4"))
        
        if not video_files:
            print("No MP4 files found!")
            return
        
        print(f"Found {len(video_files)} videos to process")
        
        for video_path in tqdm(video_files, desc="Processing videos"):
            filename = os.path.basename(video_path)
            output_path = os.path.join(output_dir, filename)
            self.process_video(video_path, output_path)
        
        # Print summary
        total = self.processed_count + self.failed_count
        success_rate = (self.processed_count / total * 100) if total > 0 else 0
        
        print(f"\n=== Processing Complete ===")
        print(f"✅ Successful: {self.processed_count}")
        print(f"❌ Failed: {self.failed_count}")
        print(f"📊 Success Rate: {success_rate:.1f}%")

def main():
    parser = argparse.ArgumentParser(description="Bottom-half face lip cropping")
    parser.add_argument('--input', required=True, help='Input directory with MP4 files')
    parser.add_argument('--output', required=True, help='Output directory')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input):
        print(f"Input directory does not exist: {args.input}")
        return
    
    os.makedirs(args.output, exist_ok=True)
    
    cropper = BottomHalfLipCropper()
    cropper.process_directory(args.input, args.output)

if __name__ == "__main__":
    main()
