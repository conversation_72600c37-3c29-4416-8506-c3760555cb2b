#!/usr/bin/env python3
"""
ICU Lipreading Preprocessing (MediaPipe Only, Real Videos)
----------------------------------------------------------
Detects and crops the lip region frame-by-frame using MediaPipe FaceMesh.
Outputs consistent 96x64 grayscale clips (24 frames @ 15 fps).
"""

import os
import cv2
import numpy as np
from tqdm import tqdm
import mediapipe as mp
import argparse
import logging

# ---- CONFIG ----
TARGET_SIZE = (96, 64)   # (width, height)
TARGET_FRAMES = 24
TARGET_FPS = 15
MIN_FRAMES = 8

# ---- Logging ----
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s")
logger = logging.getLogger("lip_preprocess")

# ---- MediaPipe ----
mp_face_mesh = mp.solutions.face_mesh.FaceMesh(
    static_image_mode=False,
    refine_landmarks=False,
    max_num_faces=1,
    min_detection_confidence=0.4,
    min_tracking_confidence=0.4
)

# Lip landmark indices (outer + inner)
LIP_INDICES = [
    61, 78, 191, 80, 81, 82, 13, 312, 308, 324, 318, 402, 317, 14,
    87, 178, 88, 95, 78, 61
]

def clahe_gray(img):
    """Contrast enhancement."""
    clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
    return clahe.apply(img)

def z_norm(img):
    """Z-normalisation with rescaling."""
    img = img.astype(np.float32)
    mean, std = img.mean(), img.std() or 1.0
    norm = (img - mean) / std
    norm = (norm - norm.min()) / (norm.max() - norm.min() + 1e-6) * 255
    return np.clip(norm, 0, 255).astype(np.uint8)

def detect_lips(frame):
    """Detect and crop lips from frame using MediaPipe landmarks."""
    h, w, _ = frame.shape
    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = mp_face_mesh.process(rgb)
    if not results.multi_face_landmarks:
        return None

    pts = []
    for idx in LIP_INDICES:
        lm = results.multi_face_landmarks[0].landmark[idx]
        pts.append((int(lm.x * w), int(lm.y * h)))
    if not pts:
        return None

    xs = [p[0] for p in pts]
    ys = [p[1] for p in pts]
    x1, x2 = max(0, min(xs)), min(w, max(xs))
    y1, y2 = max(0, min(ys)), min(h, max(ys))

    # Add padding
    pad_x = int((x2 - x1) * 0.3)
    pad_y = int((y2 - y1) * 0.4)
    x1, x2 = max(0, x1 - pad_x), min(w, x2 + pad_x)
    y1, y2 = max(0, y1 - pad_y), min(h, y2 + pad_y)

    # Ensure valid region
    if x2 <= x1 or y2 <= y1:
        return None
    crop = frame[y1:y2, x1:x2]
    if crop.size == 0:
        return None

    gray = cv2.cvtColor(crop, cv2.COLOR_BGR2GRAY)
    return cv2.resize(gray, TARGET_SIZE)

def extract_frames(video_path):
    """Sample evenly spaced frames."""
    cap = cv2.VideoCapture(video_path)
    if not cap.isOpened():
        return []

    total = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
    if total < MIN_FRAMES:
        cap.release()
        return []

    idxs = np.linspace(0, total - 1, TARGET_FRAMES).astype(int)
    frames = []
    for i in idxs:
        cap.set(cv2.CAP_PROP_POS_FRAMES, i)
        ret, frame = cap.read()
        if ret and frame is not None:
            frames.append(frame)
    cap.release()
    return frames

def process_video(path_in, path_out):
    """Process one video into lip crops."""
    frames = extract_frames(path_in)
    if len(frames) < TARGET_FRAMES:
        logger.warning(f"Skipped (too few frames): {os.path.basename(path_in)}")
        return False

    processed = []
    last_valid = None
    for f in frames:
        roi = detect_lips(f)
        if roi is None:
            roi = last_valid
        if roi is None:
            continue
        roi = clahe_gray(roi)
        roi = z_norm(roi)
        processed.append(roi)
        last_valid = roi

    if len(processed) < TARGET_FRAMES // 2:
        logger.warning(f"Failed detection: {os.path.basename(path_in)}")
        return False

    os.makedirs(os.path.dirname(path_out), exist_ok=True)
    out = cv2.VideoWriter(
        path_out, cv2.VideoWriter_fourcc(*'mp4v'), TARGET_FPS, TARGET_SIZE, isColor=False
    )
    for p in processed[:TARGET_FRAMES]:
        out.write(p)
    out.release()
    logger.info(f"✅ {os.path.basename(path_in)} processed")
    return True

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--input", required=True, help="Input directory with MP4 videos")
    parser.add_argument("--output", required=True, help="Output directory")
    args = parser.parse_args()

    videos = []
    for ext in ("*.mp4", "*.MP4"):
        videos.extend(
            [os.path.join(dp, f)
             for dp,_,fn in os.walk(args.input)
             for f in fn if f.endswith(ext.split('*')[-1])]
        )
    logger.info(f"Found {len(videos)} videos")

    success, fail = 0, 0
    for v in tqdm(videos, desc="Processing"):
        name = os.path.splitext(os.path.basename(v))[0] + ".mp4"
        out = os.path.join(args.output, name)
        if process_video(v, out):
            success += 1
        else:
            fail += 1

    logger.info(f"Done ✅ {success} | ❌ {fail}")

if __name__ == "__main__":
    main()
