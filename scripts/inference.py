"""
Inference Script for ICU Lipreading Project
Handles real-time inference and batch prediction
"""

import torch
import torch.nn as nn
import numpy as np
import cv2
import argparse
from pathlib import Path
import json
import sys
from typing import Dict, List, Tuple, Optional, Union
import time

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

from utils import get_config
from models.lipnet import create_model
from scripts.crop_roi import ROICropper
from scripts.preprocess_videos import VideoPreprocessor

class LipreadingInference:
    """Real-time lipreading inference system"""
    
    def __init__(self, checkpoint_path: str, config_path: Optional[str] = None):
        
        # Load configuration
        if config_path:
            import yaml
            with open(config_path, 'r') as f:
                self.config = yaml.safe_load(f)
        else:
            self.config = get_config().config
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # Load model
        self.model = self.load_model(checkpoint_path)
        
        # Initialize preprocessing components
        self.roi_cropper = ROICropper(target_size=(96, 64))
        self.preprocessor = VideoPreprocessor(
            target_fps=self.config['preprocessing']['fps'],
            target_frames=self.config['preprocessing']['num_frames'],
            target_size=tuple(self.config['preprocessing']['roi_size'])
        )
        
        # Inference configuration
        inference_config = self.config.get('inference', {})
        self.temporal_crops = inference_config.get('temporal_crops', 5)
        self.spatial_crops = inference_config.get('spatial_crops', 3)
        self.confidence_threshold = inference_config.get('confidence_threshold', 0.45)
        
        # Class labels
        self.class_names = self.config.get('class_names', [])
        
        print(f"🚀 Initialized inference system on {self.device}")
        print(f"📊 Loaded {len(self.class_names)} classes")
        print(f"🎯 Confidence threshold: {self.confidence_threshold}")
    
    def load_model(self, checkpoint_path: str) -> nn.Module:
        """Load trained model from checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # Get model config
        if 'config' in checkpoint:
            model_config = checkpoint['config']
        else:
            model_config = self.config
        
        # Create and load model
        model = create_model(model_config).to(self.device)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        print(f"✅ Loaded model from: {checkpoint_path}")
        return model
    
    def preprocess_video(self, video_path: Union[str, Path]) -> Optional[torch.Tensor]:
        """Preprocess video for inference"""
        video_path = Path(video_path)
        
        if not video_path.exists():
            print(f"❌ Video file not found: {video_path}")
            return None
        
        # Extract and preprocess frames
        frames = self.preprocessor.extract_frames(video_path)
        if frames is None:
            return None
        
        # Temporal alignment
        frames = self.preprocessor.temporal_align(frames)
        
        # Normalize
        frames = self.preprocessor.normalize_frames(frames)
        
        # Convert to tensor and add batch dimension
        frames_tensor = torch.from_numpy(frames).float()
        frames_tensor = frames_tensor.unsqueeze(0).unsqueeze(1)  # (1, 1, T, H, W)
        
        return frames_tensor
    
    def predict_single(self, video_tensor: torch.Tensor, use_tta: bool = True) -> Dict[str, any]:
        """Predict on a single video"""
        video_tensor = video_tensor.to(self.device)
        
        with torch.no_grad():
            if use_tta:
                # Test-time augmentation
                all_logits = []
                
                for t_crop in range(self.temporal_crops):
                    for s_crop in range(self.spatial_crops):
                        # Apply TTA transforms
                        aug_video = self.apply_tta_transform(
                            video_tensor, t_crop, s_crop
                        )
                        
                        # Forward pass
                        logits = self.model(aug_video)
                        all_logits.append(logits)
                
                # Average predictions
                avg_logits = torch.stack(all_logits).mean(dim=0)
            else:
                # Single forward pass
                avg_logits = self.model(video_tensor)
            
            # Get predictions
            probabilities = torch.softmax(avg_logits, dim=1)
            confidence, predicted_idx = torch.max(probabilities, dim=1)
            
            confidence = confidence.item()
            predicted_idx = predicted_idx.item()
            
            # Check confidence threshold
            if confidence < self.confidence_threshold:
                prediction = {
                    'predicted_class': 'UNCERTAIN',
                    'predicted_idx': -1,
                    'confidence': confidence,
                    'abstain': True,
                    'all_probabilities': probabilities.cpu().numpy().tolist()
                }
            else:
                predicted_class = self.class_names[predicted_idx] if predicted_idx < len(self.class_names) else f"Class_{predicted_idx}"
                prediction = {
                    'predicted_class': predicted_class,
                    'predicted_idx': predicted_idx,
                    'confidence': confidence,
                    'abstain': False,
                    'all_probabilities': probabilities.cpu().numpy().tolist()
                }
        
        return prediction
    
    def apply_tta_transform(self, video: torch.Tensor, t_crop: int, s_crop: int) -> torch.Tensor:
        """Apply test-time augmentation transforms"""
        B, C, T, H, W = video.shape
        
        # Temporal cropping
        if self.temporal_crops > 1:
            crop_size = max(1, T // self.temporal_crops)
            start_t = t_crop * crop_size
            end_t = min(start_t + crop_size, T)
            video = video[:, :, start_t:end_t, :, :]
            
            # Pad if necessary
            if video.shape[2] < T:
                pad_size = T - video.shape[2]
                video = torch.cat([video, video[:, :, -1:].repeat(1, 1, pad_size, 1, 1)], dim=2)
        
        # Spatial cropping
        if self.spatial_crops > 1:
            crop_h, crop_w = int(H * 0.9), int(W * 0.9)
            
            if s_crop == 0:  # Center
                start_h, start_w = (H - crop_h) // 2, (W - crop_w) // 2
            elif s_crop == 1:  # Top-left
                start_h, start_w = 0, 0
            else:  # Bottom-right
                start_h, start_w = H - crop_h, W - crop_w
            
            video = video[:, :, :, start_h:start_h+crop_h, start_w:start_w+crop_w]
            
            # Resize back
            video = torch.nn.functional.interpolate(
                video.view(-1, C, H, W), size=(H, W), mode='bilinear', align_corners=False
            ).view(B, C, T, H, W)
        
        return video
    
    def predict_video_file(self, video_path: Union[str, Path], use_tta: bool = True) -> Dict[str, any]:
        """Predict on a video file"""
        start_time = time.time()
        
        # Preprocess video
        video_tensor = self.preprocess_video(video_path)
        if video_tensor is None:
            return {'error': 'Failed to preprocess video'}
        
        # Make prediction
        prediction = self.predict_single(video_tensor, use_tta=use_tta)
        
        # Add timing info
        prediction['processing_time'] = time.time() - start_time
        prediction['video_path'] = str(video_path)
        
        return prediction
    
    def batch_predict(self, video_paths: List[Union[str, Path]], 
                     use_tta: bool = True, batch_size: int = 8) -> List[Dict[str, any]]:
        """Predict on multiple videos"""
        results = []
        
        print(f"🎬 Processing {len(video_paths)} videos...")
        
        for i in range(0, len(video_paths), batch_size):
            batch_paths = video_paths[i:i+batch_size]
            batch_results = []
            
            for video_path in batch_paths:
                result = self.predict_video_file(video_path, use_tta=use_tta)
                batch_results.append(result)
                
                # Print progress
                if not result.get('error'):
                    status = "✅" if not result['abstain'] else "⚠️"
                    print(f"{status} {Path(video_path).name}: {result['predicted_class']} ({result['confidence']:.3f})")
                else:
                    print(f"❌ {Path(video_path).name}: {result['error']}")
            
            results.extend(batch_results)
        
        return results
    
    def create_prediction_server(self, host: str = '0.0.0.0', port: int = 8000):
        """Create a simple HTTP server for predictions"""
        from flask import Flask, request, jsonify
        import tempfile
        import os
        
        app = Flask(__name__)
        
        @app.route('/predict', methods=['POST'])
        def predict_endpoint():
            try:
                # Check if video file is in request
                if 'video' not in request.files:
                    return jsonify({'error': 'No video file provided'}), 400
                
                video_file = request.files['video']
                if video_file.filename == '':
                    return jsonify({'error': 'No video file selected'}), 400
                
                # Save uploaded file temporarily
                with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as tmp_file:
                    video_file.save(tmp_file.name)
                    
                    # Make prediction
                    result = self.predict_video_file(tmp_file.name, use_tta=True)
                    
                    # Clean up
                    os.unlink(tmp_file.name)
                    
                    return jsonify(result)
                    
            except Exception as e:
                return jsonify({'error': str(e)}), 500
        
        @app.route('/health', methods=['GET'])
        def health_check():
            return jsonify({'status': 'healthy', 'model_loaded': True})
        
        print(f"🌐 Starting prediction server on {host}:{port}")
        app.run(host=host, port=port, debug=False)

def main():
    parser = argparse.ArgumentParser(description="ICU Lipreading Inference")
    parser.add_argument("--checkpoint", "-c", type=str, required=True, help="Model checkpoint path")
    parser.add_argument("--video", "-v", type=str, help="Single video file to predict")
    parser.add_argument("--batch", "-b", type=str, help="Directory with videos for batch prediction")
    parser.add_argument("--output", "-o", type=str, help="Output JSON file for batch results")
    parser.add_argument("--config", type=str, help="Custom config file")
    parser.add_argument("--no-tta", action="store_true", help="Disable test-time augmentation")
    parser.add_argument("--server", action="store_true", help="Start prediction server")
    parser.add_argument("--host", type=str, default="0.0.0.0", help="Server host")
    parser.add_argument("--port", type=int, default=8000, help="Server port")
    
    args = parser.parse_args()
    
    # Initialize inference system
    inference = LipreadingInference(args.checkpoint, args.config)
    
    if args.server:
        # Start prediction server
        inference.create_prediction_server(args.host, args.port)
    
    elif args.video:
        # Single video prediction
        result = inference.predict_video_file(args.video, use_tta=not args.no_tta)
        
        print(f"\n🎯 Prediction Results:")
        print(f"  Video: {args.video}")
        print(f"  Predicted Class: {result['predicted_class']}")
        print(f"  Confidence: {result['confidence']:.4f}")
        print(f"  Processing Time: {result['processing_time']:.2f}s")
        
        if result['abstain']:
            print(f"  ⚠️  Low confidence - abstaining from prediction")
    
    elif args.batch:
        # Batch prediction
        video_dir = Path(args.batch)
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv'}
        video_files = [f for f in video_dir.rglob("*") if f.suffix.lower() in video_extensions]
        
        if not video_files:
            print(f"❌ No video files found in {video_dir}")
            return
        
        results = inference.batch_predict(video_files, use_tta=not args.no_tta)
        
        # Save results if output specified
        if args.output:
            output_path = Path(args.output)
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"💾 Saved results to: {output_path}")
        
        # Print summary
        successful = [r for r in results if not r.get('error') and not r['abstain']]
        abstained = [r for r in results if not r.get('error') and r['abstain']]
        errors = [r for r in results if r.get('error')]
        
        print(f"\n📊 Batch Prediction Summary:")
        print(f"  Total videos: {len(results)}")
        print(f"  Successful predictions: {len(successful)}")
        print(f"  Abstained (low confidence): {len(abstained)}")
        print(f"  Errors: {len(errors)}")
    
    else:
        print("❌ Please specify --video, --batch, or --server")

if __name__ == "__main__":
    main()
