#!/bin/bash
# Automated retry script for failed video preprocessing
# Run this script to execute all phases in sequence

set -e  # Exit on any error

# Configuration
INPUT="/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker"
OUTPUT="/Users/<USER>/Desktop/LRP final/data/processed/test_clips_fixed"
RETRY_DIR="/Users/<USER>/Desktop/LRP final/data/to_retry"
PREPROCESS_SCRIPT="/Users/<USER>/Desktop/LRP final/scripts/preprocess_videos.py"

echo "=== Video Retry Processing Script ==="
echo "Input: $INPUT"
echo "Output: $OUTPUT"
echo "Retry Dir: $RETRY_DIR"
echo ""

# Create retry directory
mkdir -p "$RETRY_DIR"

echo "=== Phase A: Finding failed videos ==="
cd "/Users/<USER>/Desktop/LRP final"
source venv_mediapipe/bin/activate
python3 - <<'PY'
import os, glob, shutil
from pathlib import Path

inp = Path(os.path.expanduser(os.environ.get("INPUT", "/Users/<USER>/Desktop/LRP final/data/raw/one_video_from_each_speaker")))
out = Path(os.path.expanduser(os.environ.get("OUTPUT", "/Users/<USER>/Desktop/LRP final/data/processed/test_clips_fixed")))
retry = Path(os.path.expanduser(os.environ.get("RETRY_DIR", "/Users/<USER>/Desktop/LRP final/data/to_retry")))

exts = ("*.mp4", "*.MP4", "*.mov", "*.MOV", "*.mpg", "*.mpeg", "*.MPEG")
missing = []

print(f"Scanning input directory: {inp}")
print(f"Checking output directory: {out}")

for ext in exts:
    for p in inp.rglob(ext):
        rel = p.relative_to(inp)
        outp = out / (rel.stem + ".mp4")
        
        if not outp.exists() or outp.stat().st_size < 2000:
            missing.append(p)
            # Copy to retry folder
            dest = retry / p.name
            shutil.copy2(p, dest)
            print(f"  Copied failed: {p.name}")

print(f"\nFound {len(missing)} missing/failed videos")
print(f"Copied to retry directory: {retry}")
PY

echo ""
echo "=== Phase B: Re-encoding videos ==="
REENC_DIR="$RETRY_DIR/reencoded"
mkdir -p "$REENC_DIR"

echo "Re-encoding videos to fix container/codec issues..."
for video_file in "$RETRY_DIR"/*.{mp4,MP4,mov,MOV,mpg,mpeg,MPEG}; do
    if [ -f "$video_file" ]; then
        fn=$(basename "$video_file")
        output="$REENC_DIR/${fn%.*}.mp4"
        echo "  Re-encoding: $fn"
        ffmpeg -y -hide_banner -loglevel error -i "$video_file" \
          -vf "fps=15,scale=640:480,format=yuv420p" \
          -c:v libx264 -preset fast -crf 18 -an \
          "$output" 2>/dev/null || echo "  Failed: $fn"
    fi
done

echo ""
echo "=== Phase C: Applying jitter augmentation ==="
JITTER_DIR="$RETRY_DIR/jittered"
mkdir -p "$JITTER_DIR"

# Create jitter augmentation script
cat > /tmp/jitter_augment.py <<'PY'
import cv2, numpy as np, os, sys, glob
from pathlib import Path

if len(sys.argv) != 3:
    print("Usage: python jitter_augment.py <src_dir> <dst_dir>")
    sys.exit(1)

src = Path(sys.argv[1])
dst = Path(sys.argv[2])
dst.mkdir(exist_ok=True)

files = list(src.glob("*.mp4"))
print(f"Processing {len(files)} videos for jitter augmentation...")

for f in files:
    print(f"  Processing: {f.name}")
    cap = cv2.VideoCapture(str(f))
    if not cap.isOpened():
        print(f"    Skip: cannot open {f.name}")
        continue
    
    fps = cap.get(cv2.CAP_PROP_FPS) or 15
    w = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH) or 640)
    h = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT) or 480)
    
    fourcc = cv2.VideoWriter_fourcc(*'mp4v')
    outp = dst / f.name
    out = cv2.VideoWriter(str(outp), fourcc, 15.0, (w, h))
    
    # Fixed seed for reproducible jitter
    rng = np.random.default_rng(42)
    frame_count = 0
    
    while True:
        ok, fr = cap.read()
        if not ok:
            break
        
        # Small random affine jitter: translate ±2 px, scale 0.997-1.003
        tx = int(rng.integers(-2, 3))
        ty = int(rng.integers(-2, 3))
        sc = float(rng.uniform(0.997, 1.003))
        
        M = np.array([[sc, 0, tx], [0, sc, ty]], dtype=np.float32)
        jittered = cv2.warpAffine(fr, M, (w, h), borderMode=cv2.BORDER_REFLECT)
        
        # Brightness wobble
        bmul = float(rng.uniform(0.98, 1.02))
        jittered = cv2.convertScaleAbs(jittered, alpha=bmul, beta=0)
        
        out.write(jittered)
        frame_count += 1
    
    cap.release()
    out.release()
    print(f"    Augmented: {f.name} ({frame_count} frames)")

print("Jitter augmentation completed!")
PY

# Run jitter augmentation
cd "/Users/<USER>/Desktop/LRP final"
source venv_mediapipe/bin/activate
python3 /tmp/jitter_augment.py "$REENC_DIR" "$JITTER_DIR"

echo ""
echo "=== Phase D: Re-running preprocessing ==="
OUTPUT_RETRY="$RETRY_DIR/processed"
mkdir -p "$OUTPUT_RETRY"

echo "Running preprocessing on jittered videos..."
cd "/Users/<USER>/Desktop/LRP final"
source venv_mediapipe/bin/activate

python3 "$PREPROCESS_SCRIPT" --input "$JITTER_DIR" --output "$OUTPUT_RETRY" --verbose

echo ""
echo "=== Copying successful results back ==="
success_count=0
for video in "$OUTPUT_RETRY"/*.mp4; do
    if [ -f "$video" ]; then
        filename=$(basename "$video")
        if [ ! -f "$OUTPUT/$filename" ]; then
            cp "$video" "$OUTPUT/"
            echo "  Copied: $filename"
            ((success_count++))
        fi
    fi
done

echo ""
echo "=== Final Report ==="
echo "Successfully processed and copied: $success_count videos"

# Show remaining failures
echo ""
echo "=== Checking for remaining failures ==="
cd "/Users/<USER>/Desktop/LRP final"
source venv_mediapipe/bin/activate
python3 - <<'PY'
import os, glob
from pathlib import Path

out_dir = Path(os.path.expanduser(os.environ.get("OUTPUT", "/Users/<USER>/Desktop/LRP final/data/processed/test_clips_fixed")))
retry_dir = Path(os.path.expanduser(os.environ.get("RETRY_DIR", "/Users/<USER>/Desktop/LRP final/data/to_retry")))

missing = []
for f in retry_dir.glob("*.mp4"):
    expected_output = out_dir / f.name
    if not expected_output.exists():
        missing.append(f.name)

print(f"Remaining missing videos: {len(missing)}")
for m in missing:
    print(f"  Still missing: {m}")

if len(missing) == 0:
    print("🎉 All videos successfully processed!")
PY

echo ""
echo "=== Retry process completed! ==="
echo "Check the results in: $OUTPUT"
echo "Retry artifacts saved in: $RETRY_DIR"
