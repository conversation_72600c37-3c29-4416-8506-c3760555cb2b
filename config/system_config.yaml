paths:
  local_root: "/Users/<USER>/Desktop/LRP final"
  cloud_root: "/home/<USER>/SageMaker/LRP_final_5.10.25"
  s3_bucket: "s3://icudatasetphrasesfortesting/speaker_sets_5.10.25"
  
data:
  raw_videos_path: "data/raw"
  processed_videos_path: "data/processed"
  manifests_path: "data/manifests"
  splits_path: "data/splits"
  
model:
  pretrained_path: "models/pretrained"
  checkpoints_path: "models/checkpoints"
  
preprocessing:
  roi_size: [96, 64]  # width, height
  num_frames: 24
  fps: 15
  grayscale: true
  
training:
  device: "cuda"
  batch_size: 32
  learning_rate: 3e-4
  num_epochs: 100
  early_stop_patience: 10
  early_stop_metric: "macro_f1"
  early_stop_threshold: 0.80
  
augmentation:
  scale_range: [0.9, 1.1]
  shift_range: 0.1
  brightness_range: [0.8, 1.2]
  contrast_range: [0.8, 1.2]
  gamma_range: [0.8, 1.2]
  blur_kernel_size: 3
  temporal_crop: true
  mixup_alpha: 0.2
  
loss:
  label_smoothing: 0.05
  prototypical_weight: 0.3
  
optimizer:
  name: "AdamW"
  weight_decay: 1e-4
  
scheduler:
  name: "cosine"
  warmup_epochs: 5
  
regularization:
  dropout: 0.3
  
inference:
  temporal_crops: 5
  spatial_crops: 3
  confidence_threshold: 0.45
  
validation:
  cv_folds: 5
  speaker_disjoint: true
