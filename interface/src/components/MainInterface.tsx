import { FC, useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowLeft, Circle, FlipHorizontal } from "lucide-react";
import useCamera from "@/lib/useCamera";
import useVideoRecorder from "@/lib/useVideoRecorder";
import { apiRequest } from "@/lib/queryClient";

interface MainInterfaceProps {
  onBackToWelcome: () => void;
  onShowResults: (prediction: string, videoUrl: string) => void;
  onError: (message: string) => void;
}

const MainInterface: FC<MainInterfaceProps> = ({
  onBackToWelcome,
  onShowResults,
  onError,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const { stream, hasPermission, requestCameraPermission, facingMode, flipCamera } = useCamera();
  const { 
    isRecording, 
    recordedVideoUrl, 
    startRecording, 
    stopRecording 
  } = useVideoRecorder({ stream, videoRef });
  
  const [isProcessing, setIsProcessing] = useState(false);

  useEffect(() => {
    if (videoRef.current && stream) {
      videoRef.current.srcObject = stream;
    }
  }, [stream, videoRef]);

  // Auto-stop recording after 10 seconds without visible countdown
  useEffect(() => {
    if (isRecording) {
      const autoStopTimer = setTimeout(() => {
        stopRecording();
      }, 10000); // 10 seconds
      
      return () => clearTimeout(autoStopTimer);
    }
  }, [isRecording, stopRecording]);

  const handleRecordClick = () => {
    if (isProcessing || !hasPermission) return;
    
    // If already recording, stop it
    if (isRecording) {
      stopRecording();
      return;
    }
    
    // Start recording without any delay or countdown
    startRecording();
  };

  // Function to simulate processing a video for testing
  const simulateProcessing = () => {
    console.log("Simulating video processing...");
    setIsProcessing(true);
    
    // Sample medical phrases (same as server)
    const predictions = [
      "I need water",
      "I need suctioning",
      "I'm in pain",
      "I need my medication",
      "I feel nauseous",
      "I can't breathe",
      "I'm feeling dizzy",
      "Can you call the nurse",
      "Help me please",
      "Thank you"
    ];
    
    // Simulate processing delay
    setTimeout(() => {
      const randomIndex = Math.floor(Math.random() * predictions.length);
      setIsProcessing(false);
      onShowResults(predictions[randomIndex], recordedVideoUrl || "about:blank");
    }, 1500);
  };

  // Handle the manual "Test Recording" button
  const handleTestProcessing = () => {
    simulateProcessing();
  };

  // Keep track of processed videos to avoid reprocessing
  const processedVideos = useRef<Set<string>>(new Set());
  
  useEffect(() => {
    if (!isRecording && recordedVideoUrl && !processedVideos.current.has(recordedVideoUrl)) {
      // Mark this video as processed to prevent multiple processing
      processedVideos.current.add(recordedVideoUrl);
      
      setIsProcessing(true);
      console.log("Processing recorded video");
      
      // Simplified processing for testing
      try {
        // For testing simplicity, we'll just simulate the API call
        simulateProcessing();
      } catch (error) {
        console.error("Processing error:", error);
        setIsProcessing(false);
        onError(`Failed to process video: ${error instanceof Error ? error.message : String(error)}`);
      }
    }
  }, [isRecording, recordedVideoUrl, onShowResults, onError]);

  return (
    <div className="relative h-screen">
      {/* Header */}
      <div className="flex items-center justify-between bg-primary px-4 py-3">
        <Button 
          variant="ghost" 
          size="icon" 
          onClick={onBackToWelcome}
          className="text-white"
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <h1 className="text-xl font-bold text-white">Read My Lips</h1>
        
        {/* MyLearning logo based on SMHS website */}
        <div className="flex items-center justify-center h-6 w-6">
          <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
            <circle cx="12" cy="12" r="8" fill="white" opacity="0.5" />
            <circle cx="7" cy="12" r="2" fill="white" />
            <circle cx="17" cy="12" r="2" fill="white" />
            <circle cx="12" cy="7" r="2" fill="white" />
            <circle cx="12" cy="17" r="2" fill="white" />
          </svg>
        </div>
      </div>
      
      {/* Video Container */}
      <div className="relative w-full h-[calc(100vh-72px)] bg-black overflow-hidden">
        {/* Live video feed - slightly blurred background */}
        <video 
          ref={videoRef}
          className="absolute inset-0 w-full h-full object-cover blur-[2px]"
          autoPlay 
          muted 
          playsInline
        />
        
        {/* Oval frame guide */}
        <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
          {/* Oval mask overlay with sharp/unblurred video in center */}
          <div className="relative h-[140px] w-[280px]">
            {/* Clear video inside the oval */}
            <div className="absolute inset-0 rounded-full overflow-hidden z-20">
              <video 
                className="absolute w-[calc(100vw)] h-[calc(100vh-120px)] object-cover"
                style={{
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                  filter: 'none',
                  willChange: 'transform', /* Optimize for smoother rendering */
                }}
                autoPlay 
                muted 
                playsInline
                ref={(el) => {
                  if (el && stream) {
                    // Directly set the unblurred stream to this video element
                    el.srcObject = stream;
                    // Ensure video is stable by setting playback quality
                    el.setAttribute('playsinline', '');
                  }
                }}
              />
            </div>
            
            {/* Oval border/guide */}
            <div 
              className="absolute inset-0 rounded-full border-4 border-white/40 z-30"
              style={{
                boxShadow: '0 0 0 9999px rgba(0, 0, 0, 0.35)',
              }}
            ></div>

            {/* Text guidance above oval */}
            <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 text-white text-center text-sm bg-black/60 px-4 py-2 rounded-full whitespace-nowrap z-30">
              Position your face in the oval
            </div>
          </div>
        </div>
        
        {/* Camera permission notification */}
        {!hasPermission && (
          <div className="absolute inset-0 flex flex-col items-center justify-center bg-black/80 text-white p-6">
            <svg 
              xmlns="http://www.w3.org/2000/svg" 
              className="h-12 w-12 mb-4"
              fill="none" 
              viewBox="0 0 24 24" 
              stroke="currentColor"
            >
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" 
              />
            </svg>
            <p className="text-center mb-4">Camera access is required for this application to function.</p>
            <Button 
              onClick={requestCameraPermission}
              className="bg-white text-primary font-semibold"
            >
              Enable Camera
            </Button>
          </div>
        )}
        
        {/* Static recording indicator (no animation) */}
        {isRecording && (
          <div className="absolute top-4 left-4 bg-accent text-white px-3 py-1 rounded-full flex items-center opacity-70">
            <div className="h-2 w-2 bg-white rounded-full mr-2"></div>
            <span className="text-sm font-medium">Recording</span>
          </div>
        )}
        
        {/* Countdown timer removed from inside the oval */}
        
        {/* Loading indicator */}
        {isProcessing && (
          <div className="absolute inset-0 bg-black/70 flex flex-col items-center justify-center">
            <div className="w-16 h-16 border-4 border-t-accent border-r-accent border-b-transparent border-l-transparent rounded-full animate-spin mb-4"></div>
            <p className="text-white font-medium">Processing...</p>
          </div>
        )}
      </div>
      
      {/* Camera Controls */}
      <div className="absolute bottom-24 left-1/2 transform -translate-x-1/2 flex items-center justify-center z-10 w-full px-6">
        <Button
          onClick={flipCamera}
          disabled={isRecording || isProcessing || !hasPermission}
          className="bg-primary text-white font-medium py-2 px-4 rounded-md hover:bg-primary/90 transition-colors w-4/5 max-w-xs"
          style={{ cursor: 'pointer', pointerEvents: 'auto' }}
          title={`Switch to ${facingMode === 'user' ? 'back' : 'front'} camera`}
        >
          <FlipHorizontal className="h-5 w-5 mr-2" />
          <span>Flip Camera</span>
        </Button>
      </div>
      
      {/* Record Button */}
      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 flex items-center justify-center z-50">
        <Button
          onClick={handleRecordClick}
          disabled={isProcessing || !hasPermission}
          className={`w-20 h-20 rounded-full flex items-center justify-center shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-all duration-200 ${
            isProcessing 
              ? "bg-gray-500 cursor-not-allowed" 
              : isRecording
                ? "bg-red-600 hover:bg-red-700"
                : "bg-green-600 hover:bg-green-700"
          }`}
          style={{ cursor: 'pointer', pointerEvents: 'auto', zIndex: 50 }}
          tabIndex={0}
          onKeyPress={(e) => e.key === 'Enter' && !isProcessing && hasPermission && handleRecordClick()}
          aria-label={isRecording ? "Stop Recording" : "Start Recording"}
        >
          {isRecording ? (
            <div className="w-8 h-8 bg-white rounded-sm"></div> // Stop symbol
          ) : (
            <div className="w-8 h-8 bg-white rounded-full"></div> // Record symbol
          )}
        </Button>
        
        {/* Removed countdown display completely */}
      </div>
    </div>
  );
};

export default MainInterface;
