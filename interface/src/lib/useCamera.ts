import { useState, useEffect, useCallback } from "react";

type FacingMode = "user" | "environment";

interface UseCameraReturn {
  stream: MediaStream | null;
  hasPermission: boolean;
  requestCameraPermission: () => Promise<void>;
  error: Error | null;
  facingMode: FacingMode;
  flipCamera: () => Promise<void>;
}

const useCamera = (): UseCameraReturn => {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [hasPermission, setHasPermission] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [facingMode, setFacingMode] = useState<FacingMode>("user");

  const getCamera = async (mode: FacingMode) => {
    // Stop any existing tracks
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }

    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: mode,
          width: { ideal: 1280 },
          height: { ideal: 720 }
        },
        audio: false
      });
      
      setStream(mediaStream);
      setHasPermission(true);
      setError(null);
      return mediaStream;
    } catch (err) {
      setStream(null);
      setHasPermission(false);
      setError(err instanceof Error ? err : new Error('Unknown error occurred'));
      throw err;
    }
  };

  const requestCameraPermission = useCallback(async () => {
    try {
      await getCamera(facingMode);
    } catch (err) {
      console.error("Failed to get camera permission:", err);
    }
  }, [facingMode]);

  const flipCamera = async () => {
    const newMode = facingMode === "user" ? "environment" : "user";
    setFacingMode(newMode);
    try {
      await getCamera(newMode);
    } catch (err) {
      console.error("Failed to flip camera:", err);
    }
  };

  useEffect(() => {
    requestCameraPermission();

    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);

  return {
    stream,
    hasPermission,
    requestCameraPermission,
    error,
    facingMode,
    flipCamera
  };
};

export default useCamera;
