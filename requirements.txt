# ICU Lipreading Project - Python Dependencies

# Core ML/DL frameworks
torch>=2.0.0
torchvision>=0.15.0
torchaudio>=2.0.0
numpy>=1.21.0
scipy>=1.7.0

# Computer Vision
opencv-python>=4.5.0
dlib>=19.22.0
Pillow>=8.3.0

# Data Science
pandas>=1.3.0
scikit-learn>=1.0.0
matplotlib>=3.4.0
seaborn>=0.11.0

# Deep Learning Utilities
tqdm>=4.62.0
wandb>=0.12.0
tensorboard>=2.7.0

# Configuration and Utilities
PyYAML>=6.0
pathlib2>=2.3.0
argparse>=1.4.0
json5>=0.9.0

# Web Framework (for inference server)
Flask>=2.0.0
Flask-CORS>=3.0.0
requests>=2.26.0

# Audio Processing (for speech synthesis)
pydub>=0.25.0
librosa>=0.8.0

# Jupyter and Analysis
jupyter>=1.0.0
ipykernel>=6.0.0
ipywidgets>=7.6.0

# AWS Integration
boto3>=1.18.0
awscli>=1.20.0

# Development and Testing
pytest>=6.2.0
black>=21.0.0
flake8>=3.9.0
mypy>=0.910

# Additional utilities
psutil>=5.8.0
GPUtil>=1.4.0
humanize>=3.12.0
